"""
Complete Integrated Multi-Year Financial Processing Pipeline
Date: 2025-07-02 
User: Gangatharan G
Purpose: Complete end-to-end pipeline from web search to dual JSON outputs
Pipeline: Web Search → Classification → Multi-Year Financial + Latest Year Assumptions
"""

import os
import json
import asyncio
from pathlib import Path
from datetime import datetime
import argparse
import logging
import re
from typing import List, Dict, Optional, Tuple, Any

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Warning: python-dotenv not installed")

# Import all pipeline components
try:
    # Multi-Year Financial Components
    from web_search import run_downloader
    from classify import EnhancedPDFFinancialClassifier
    from single_file_debt_equity_processor import SingleFileDebtEquityProcessor
    from financial_data_pipeline_new import extract_consolidated_financials
    from prompt_scehma_new import get_dynamic_prompts
    from final_schema_transformer import FinalSchemaTransformer
    
    # Financial Assumptions Components
    from extractors import FocusedFinancialExtractor
    from calculator import FinancialMetricsCalculator
    from json_generator import FinancialJSONGenerator
    
except ImportError as e:
    print(f"Missing required component: {e}")
    print("Ensure all pipeline files are available:")
    print("  Multi-Year: web_search.py, classify.py, single_file_debt_equity_processor.py")
    print("  Multi-Year: financial_data_pipeline_new.py, prompt_scehma_new.py, final_schema_transformer.py")
    print("  Assumptions: extractors.py, calculator.py, json_generator.py")
    exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('complete_integrated_pipeline.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CompleteIntegratedPipeline:
    """Complete integrated pipeline for multi-year financial processing with assumptions"""
    
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        logger.info("="*80)
        logger.info("🚀 Complete Integrated Financial Pipeline Initialized")
        logger.info(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} UTC")
        logger.info(f"👤 User: Gangatharan G")
        logger.info("="*80)
        
        # Validate environment
        self.validate_environment()
        
        # Initialize components
        self.initialize_components()
    
    def validate_environment(self):
        """Check required environment variables"""
        required_vars = ["GEMINI_API_KEY", "OPENAI_API_KEY"]
        missing_vars = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")
        
        logger.info("✅ Environment variables validated")
    
    def initialize_components(self):
        """Initialize all pipeline components"""
        try:
            # Multi-Year Components
            self.debt_processor = SingleFileDebtEquityProcessor()
            self.schema_transformer = FinalSchemaTransformer()
            logger.info("✅ Multi-year components initialized")
            
            # Financial Assumptions Components
            self.extractor = FocusedFinancialExtractor()
            self.calculator = FinancialMetricsCalculator()
            self.json_generator = FinancialJSONGenerator()
            logger.info("✅ Financial assumptions components initialized")
            
        except Exception as e:
            logger.error(f"❌ Component initialization failed: {e}")
            raise Exception(f"Component initialization failed: {e}")
    
    def extract_year_from_filename(self, pdf_path: str) -> int:
        """Extract year from PDF filename"""
        filename = Path(pdf_path).name.lower()
        
        # Look for 4-digit year pattern
        year_match = re.search(r'20\d{2}', filename)
        if year_match:
            return int(year_match.group())
        
        # Default to current year if not found
        return datetime.now().year
    
    def auto_detect_latest_year(self, years: List[int]) -> Optional[int]:
        """Automatically detect the latest year from processed years"""
        if not years:
            return None
        return max(years)
    
    def find_classified_images(self, output_dir: Path, classification_type: str):
        """Find first image for a classification type"""
        classified_dir = output_dir / "classified_images"
        
        # Map to directory names (from classify.py)
        dir_mapping = {
            "balance_sheet": "statement_of_balance_sheet",
            "profit_loss": "statement_of_profit_and_loss", 
            "cash_flows": "statement_of_cash_flows",
            "credit_rating": "credit_rating"
        }
        
        target_dir = classified_dir / dir_mapping.get(classification_type, classification_type)
        
        if target_dir.exists():
            images = list(target_dir.glob("*.png"))
            if images:
                return str(images[0])  # Return first image
        
        return None
    
    async def get_pdfs(self, plant_name: str, local_pdfs: List[str] = None) -> List[str]:
        """Get PDFs either from local files or web search"""
        print("\n📄 STEP 1: Getting PDF files...")
        
        if local_pdfs:
            print(f"✅ Using {len(local_pdfs)} local PDF files")
            
            # Validate local files exist
            for pdf_file in local_pdfs:
                if not Path(pdf_file).exists():
                    raise FileNotFoundError(f"Local PDF not found: {pdf_file}")
            
            return local_pdfs
        else:
            print("🔍 Downloading PDFs using web search...")
            
            # Use web search to download PDFs (2020-2024 by default)
            await asyncio.get_event_loop().run_in_executor(
                None, run_downloader, plant_name, 2020, 2024
            )
            
            # Find downloaded PDFs
            safe_plant_name = plant_name.replace(" ", "_").replace('"', '')
            downloads_dir = Path("downloads") / safe_plant_name
            
            if downloads_dir.exists():
                pdf_files = list(downloads_dir.glob("*.pdf"))
                pdf_files = [str(f) for f in pdf_files]
                print(f"✅ Found {len(pdf_files)} downloaded PDFs")
                return pdf_files
            else:
                raise FileNotFoundError(f"No PDFs found in downloads directory: {downloads_dir}")
    
    async def process_single_year_classification_and_financial(self, pdf_path: str, year: int, 
                                                              plant_name: str, base_output_dir: Path):
        """Process a single year: classification + financial data extraction"""
        print(f"\n📅 Processing Year {year}: {Path(pdf_path).name}")
        
        year_result = {
            "year": year,
            "pdf_file": str(pdf_path),
            "classification_results": None,
            "debt_equity_analysis": None,
            "financial_data": None,
            "processing_status": "started"
        }
        
        try:
            # Create year-specific output directory
            year_output_dir = base_output_dir / f"year_{year}"
            year_output_dir.mkdir(exist_ok=True)
            
            # Step 1: Classification for this year
            print(f"  📊 Classifying {year} documents...")
            classifier = EnhancedPDFFinancialClassifier(batch_size=5)
            classification_results = classifier.classify_pdf_batch_by_batch(
                pdf_path=pdf_path,
                output_dir=str(year_output_dir)
            )
            
            # Find manifest file
            manifest_files = list(year_output_dir.glob("enhanced_vllm_manifest.json"))
            if not manifest_files:
                raise FileNotFoundError(f"Classification manifest not found for year {year}")
            
            manifest_path = str(manifest_files[0])
            year_result["classification_results"] = classification_results
            print(f"  ✅ {year}: Classification completed")
            
            # Step 2: Debt-Equity Processing for this year
            print(f"  💰 Extracting debt-equity data for {year}...")
            debt_equity_data = await self.debt_processor.process_single_file_adaptive(manifest_path)
            
            year_result["debt_equity_analysis"] = debt_equity_data
            print(f"  ✅ {year}: Debt-equity analysis completed")
            
            # Step 3: Financial Data Extraction for this year
            print(f"  📈 Extracting financial data for {year}...")
            
            # Find required classified images
            balance_sheet_path = self.find_classified_images(year_output_dir, "balance_sheet")
            profit_loss_path = self.find_classified_images(year_output_dir, "profit_loss")
            cash_flows_path = self.find_classified_images(year_output_dir, "cash_flows")
            credit_rating_path = self.find_classified_images(year_output_dir, "credit_rating")
            
            # Extract financial data if images available
            financial_output_path = year_output_dir / f"financial_data_{year}.json"
            
            if balance_sheet_path or profit_loss_path or cash_flows_path or credit_rating_path:
                # Use placeholder images if some are missing
                balance_sheet_path = balance_sheet_path or profit_loss_path or cash_flows_path
                profit_loss_path = profit_loss_path or balance_sheet_path or cash_flows_path
                cash_flows_path = cash_flows_path or balance_sheet_path or profit_loss_path
                credit_rating_path = credit_rating_path or balance_sheet_path
                
                # Get year-specific prompts
                year_prompts = get_dynamic_prompts(year)

                extract_consolidated_financials(
                    balance_sheet_path=balance_sheet_path,
                    profit_and_loss_path=profit_loss_path,
                    cash_flows_path=cash_flows_path,
                    credit_rating_path=credit_rating_path,
                    assets_prompt=year_prompts["assets_prompt"],
                    cashflows_prompt=year_prompts["cashflows_prompt"],
                    revenue_expenses_prompt=year_prompts["revenue_expenses_prompt"],
                    credit_rating_prompt=year_prompts["credit_rating_prompt"],
                    output_file_path=str(financial_output_path)
                )
                
                # Load financial data
                with open(financial_output_path, 'r') as f:
                    financial_data = json.load(f)
                
                year_result["financial_data"] = financial_data
                print(f"  ✅ {year}: Financial data extraction completed")
            else:
                print(f"  ⚠️ {year}: No classified financial images found")
                year_result["financial_data"] = {"error": "No classified financial images available"}
            
            year_result["processing_status"] = "completed"
            print(f"✅ Year {year} processing completed successfully!")
            
            return year_result
            
        except Exception as e:
            error_msg = f"Year {year} processing failed: {str(e)}"
            print(f"  ❌ {error_msg}")
            logger.error(error_msg)
            
            year_result["processing_status"] = "failed"
            year_result["error"] = str(e)
            
            return year_result
    
    def merge_multi_year_results(self, year_results: List[Dict]) -> Dict:
        """Merge results from multiple years"""
        print("\n🔄 Merging multi-year results...")
        
        merged_debt_equity = []
        merged_financial = {
            "assets": [],
            "cashflows": [],
            "revenue_expenses": [],
            "credit_rating": []
        }
        
        successful_years = []
        
        for year_result in year_results:
            if year_result["processing_status"] == "completed":
                successful_years.append(year_result["year"])
                
                # Merge debt-equity analysis
                debt_data = year_result.get("debt_equity_analysis", {})
                if debt_data and debt_data.get("debt_equity_analysis"):
                    debt_analysis = debt_data["debt_equity_analysis"][0]
                    debt_analysis["year"] = year_result["year"]  # Ensure year is set
                    merged_debt_equity.append(debt_analysis)
                
                # Merge financial data
                financial_data = year_result.get("financial_data", {})
                if financial_data and "error" not in financial_data:
                    # Add year to each financial component
                    for component in ["assets", "cashflows", "revenue_expenses", "credit_rating"]:
                        if component in financial_data:
                            component_data = financial_data[component]
                            if isinstance(component_data, list):
                                for item in component_data:
                                    if isinstance(item, dict):
                                        item["year"] = year_result["year"]
                                merged_financial[component].extend(component_data)
                            elif isinstance(component_data, dict):
                                component_data["year"] = year_result["year"]
                                merged_financial[component].append(component_data)
        
        print(f"✅ Merged data from {len(successful_years)} successful years: {successful_years}")
        
        return {
            "merged_debt_equity_analysis": merged_debt_equity,
            "merged_financial_data": merged_financial,
            "successful_years": successful_years,
            "total_years_processed": len(year_results),
            "successful_years_count": len(successful_years)
        }
    
    async def process_financial_assumptions_for_latest_year(self, latest_year_result: Dict, 
                                                          plant_name: str, output_dir: Path) -> Optional[Dict]:
        """Process financial assumptions for the latest year only"""
        print(f"\n📋 STEP 4: Processing Financial Assumptions for Latest Year...")
        
        try:
            latest_year = latest_year_result["year"]
            print(f"🎯 Latest Year Detected: {latest_year}")
            
            # Get classification results for latest year
            classification_results = latest_year_result.get("classification_results", {})
            if not classification_results:
                print(f"⚠️ No classification results found for year {latest_year}")
                return None
            
            # Find classified images for latest year
            year_output_dir = output_dir / f"year_{latest_year}"
            
            # Get classified images paths from manifest
            classified_images = {}
            
            # Load manifest to get all classified images
            manifest_path = year_output_dir / "enhanced_vllm_manifest.json"
            if manifest_path.exists():
                import json
                with open(manifest_path, 'r') as f:
                    manifest = json.load(f)
                
                # Get classifications from manifest
                classifications = manifest.get("classifications", {})
                for classification, data in classifications.items():
                    if "full_paths" in data and data["full_paths"]:
                        # Convert relative paths to absolute paths
                        full_paths = []
                        for path in data["full_paths"]:
                            abs_path = Path(path)
                            if not abs_path.is_absolute():
                                abs_path = Path("/Users/<USER>/Documents/mlprojects/Transition-agi-fin_new_pipeline") / path
                            full_paths.append(str(abs_path))
                        classified_images[classification] = full_paths
                        print(f"📁 {classification}: {len(full_paths)} images")
            else:
                # Fallback to old method
                classification_mappings = {
                    "Statement of Balance Sheet": "balance_sheet",
                    "Statement of Profit and Loss": "profit_loss",
                    "Statement of Cash Flows": "cash_flows",
                    "Credit Rating": "credit_rating"
                }
                
                for classification, mapped_name in classification_mappings.items():
                    image_path = self.find_classified_images(year_output_dir, mapped_name)
                    if image_path:
                        classified_images[classification] = [image_path]
            
            if not classified_images:
                print(f"⚠️ No classified images found for financial assumptions processing")
                return None
            
            print(f"📊 Found classified images for: {list(classified_images.keys())}")
            
            # Step 1: Extract financial data for assumptions (ENHANCED)
            print(f"  🔍 Extracting detailed financial data for {latest_year} (Enhanced with Notes)...")
            extracted_data = self.extractor.extract_financial_data_enhanced(classified_images)
            
            if not extracted_data:
                print(f"⚠️ Enhanced financial data extraction failed for year {latest_year}")
                print(f"  🔄 Falling back to standard extraction...")
                extracted_data = self.extractor.extract_financial_data(classified_images)
                
                if not extracted_data:
                    print(f"⚠️ Standard financial data extraction also failed for year {latest_year}")
                    return None
            
            # Step 2: Calculate financial metrics (ENHANCED)
            print(f"  🧮 Calculating financial metrics for {latest_year} (Enhanced)...")
            
            # Use enhanced calculation method
            financial_metrics = self.calculator.calculate_financial_metrics_enhanced(extracted_data)
            
            if not financial_metrics:
                print(f"⚠️ Financial metrics calculation failed for year {latest_year}")
                return None
            
            # Finalize WACC calculation
            financial_metrics = self.calculator.calculate_and_finalize_wacc(financial_metrics)
            
            # Step 3: Generate financial assumptions JSON
            print(f"  📄 Generating financial assumptions JSON for {latest_year}...")
            
            assumptions_package = self.json_generator.create_complete_output_package(
                financial_metrics=financial_metrics,
                output_dir=str(output_dir),
                pdf_path=f"{plant_name}_{latest_year}.pdf"
            )
            
            if not assumptions_package.get("success", False):
                print(f"⚠️ Financial assumptions JSON generation failed")
                return None
            
            # Extract the assumptions JSON
            assumptions_json = assumptions_package.get("financial_assumptions_json", {})
            
            # Add plant and year information
            # assumptions_json.update({
            #     "plant_name": plant_name,
            #     "year": latest_year,
            #     "processing_timestamp": datetime.utcnow().isoformat()
            # })
            
            print(f"✅ Financial assumptions processing completed for year {latest_year}")
            return assumptions_json
            
        except Exception as e:
            error_msg = f"Financial assumptions processing failed: {str(e)}"
            print(f"❌ {error_msg}")
            logger.error(error_msg)
            return None
    
    async def process_complete_pipeline(self, plant_name: str, local_pdfs: List[str] = None, 
                                      target_years: List[int] = None) -> Tuple[Optional[str], Optional[str], Dict]:
        """
        Complete integrated pipeline: Web Search → Classification → Multi-Year Financial + Latest Year Assumptions
        
        Returns:
            Tuple[financial_json_path, assumptions_json_path, processing_results]
        """
        
        print("🚀 COMPLETE INTEGRATED FINANCIAL PROCESSING PIPELINE")
        print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} UTC")
        print(f"👤 User: Gangatharan G")
        print(f"🏭 Plant: {plant_name}")
        print(f"📆 Target Years: {target_years or 'All available years'}")
        print("="*80)
        
        results = {
            "plant_name": plant_name,
            "processing_timestamp": datetime.utcnow().isoformat(),
            "user": "Gangatharan G",
            "target_years": target_years,
            "year_results": [],
            "merged_results": None,
            "latest_year_assumptions": None,
            "processing_summary": {}
        }
        
        try:
            # Step 1: Get PDFs (Web Search or Local)
            pdf_files = await self.get_pdfs(plant_name, local_pdfs)
            
            if not pdf_files:
                raise ValueError("No PDF files available for processing")
            
            # Filter by target years if specified
            if target_years:
                filtered_pdfs = []
                for pdf_file in pdf_files:
                    pdf_year = self.extract_year_from_filename(pdf_file)
                    if pdf_year in target_years:
                        filtered_pdfs.append(pdf_file)
                
                pdf_files = filtered_pdfs
                print(f"📅 Filtered to {len(pdf_files)} PDFs matching target years")
            
            # Create main output directory
            safe_plant_name = plant_name.replace(" ", "_").replace('"', '').lower()
            main_output_dir = Path(f"{safe_plant_name}_complete_pipeline_{self.timestamp}")
            main_output_dir.mkdir(exist_ok=True)
            
            # Step 2: Process Each Year (Classification + Financial Data)
            print(f"\n📊 STEP 2: Processing {len(pdf_files)} years (Classification + Financial Data)...")
            
            year_results = []
            for pdf_file in pdf_files:
                year = self.extract_year_from_filename(pdf_file)
                year_result = await self.process_single_year_classification_and_financial(
                    pdf_file, year, plant_name, main_output_dir
                )
                year_results.append(year_result)
                results["year_results"].append(year_result)
            
            # Step 3: BRANCH 1 - Multi-Year Financial Processing (ALWAYS)
            print("\n🔄 STEP 3: BRANCH 1 - Multi-Year Financial Processing (ALWAYS TRIGGERED)...")
            merged_results = self.merge_multi_year_results(year_results)
            results["merged_results"] = merged_results
            
            # Transform to final schema format
            print("  🔄 Transforming to final schema format...")
            final_schema_output = self.schema_transformer.transform_to_final_schema({
                "merged_results": merged_results,
                "merged_debt_equity_analysis": merged_results["merged_debt_equity_analysis"],
                "merged_financial_data": merged_results["merged_financial_data"],
                "detailed_year_results": year_results,
                "plant_name": plant_name
            })
            
            # Save OUTPUT 1: Multi-Year Financial JSON
            financial_json_path = main_output_dir / f"{safe_plant_name}_financial.json"
            with open(financial_json_path, 'w', encoding='utf-8') as f:
                json.dump(final_schema_output, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"✅ OUTPUT 1 SAVED: {financial_json_path}")
            print(f"📊 Multi-Year Financial Data: {len(merged_results['successful_years'])} years")
            
            # Step 4: BRANCH 2 - Financial Assumptions (LATEST YEAR ONLY)
            print("\n📋 STEP 4: BRANCH 2 - Financial Assumptions (LATEST YEAR CONDITIONAL)...")
            
            # Detect latest year
            successful_years = merged_results.get("successful_years", [])
            if successful_years:
                latest_year = self.auto_detect_latest_year(successful_years)
                print(f"🎯 Latest Year Auto-Detected: {latest_year}")
                
                # Find latest year result
                latest_year_result = None
                for year_result in year_results:
                    if year_result["year"] == latest_year and year_result["processing_status"] == "completed":
                        latest_year_result = year_result
                        break
                
                if latest_year_result:
                    # Process financial assumptions for latest year
                    assumptions_data = await self.process_financial_assumptions_for_latest_year(
                        latest_year_result, plant_name, main_output_dir
                    )
                    
                    if assumptions_data:
                        # Save OUTPUT 2: Financial Assumptions JSON
                        assumptions_json_path = main_output_dir / f"{safe_plant_name}_financial_assumptions.json"
                        with open(assumptions_json_path, 'w', encoding='utf-8') as f:
                            json.dump(assumptions_data, f, indent=2, ensure_ascii=False, default=str)
                        
                        results["latest_year_assumptions"] = assumptions_data
                        print(f"✅ OUTPUT 2 SAVED: {assumptions_json_path}")
                        print(f"📋 Financial Assumptions: Year {latest_year}")
                        
                        # Show WACC if available
                        wacc_data = assumptions_data.get("wacc_breakup", {})
                        if wacc_data and wacc_data.get("wacc"):
                            print(f"💰 WACC Calculated: {wacc_data['wacc']:.2%}")
                    else:
                        print("⚠️ Financial assumptions processing failed")
                        assumptions_json_path = None
                else:
                    print(f"⚠️ Latest year {latest_year} result not found or failed")
                    assumptions_json_path = None
            else:
                print("⚠️ No successful years found - skipping financial assumptions")
                assumptions_json_path = None
            
            # Final Summary
            successful_count = len(merged_results["successful_years"])
            total_count = len(pdf_files)
            
            print(f"\n🎉 COMPLETE PIPELINE FINISHED!")
            print("="*60)
            print(f"📁 Output Directory: {main_output_dir}")
            print(f"📊 Success Rate: {successful_count}/{total_count} years")
            print(f"✅ Successful Years: {merged_results['successful_years']}")
            print()
            print(f"📄 OUTPUT 1: {financial_json_path}")
            print(f"   • Multi-year financial data (6 fields)")
            print(f"   • Currency: {final_schema_output.get('currency', 'N/A')}")
            
            if assumptions_json_path:
                print(f"📄 OUTPUT 2: {assumptions_json_path}")
                print(f"   • Financial assumptions (Latest year: {latest_year})")
                wacc_data = results.get("latest_year_assumptions", {}).get("wacc_breakup", {})
                if wacc_data.get("wacc"):
                    print(f"   • WACC: {wacc_data['wacc']:.2%}")
            else:
                print(f"📄 OUTPUT 2: Not generated (no latest year data)")
            print("="*60)
            
            return str(financial_json_path), str(assumptions_json_path) if assumptions_json_path else None, results
            
        except Exception as e:
            error_msg = f"Complete pipeline processing failed: {str(e)}"
            print(f"\n❌ ERROR: {error_msg}")
            logger.error(error_msg)
            
            results["error"] = error_msg
            return None, None, results

def main():
    """Main CLI interface for complete integrated pipeline"""
    parser = argparse.ArgumentParser(description="Complete Integrated Multi-Year Financial Processing Pipeline")
    parser.add_argument("plant_name", help="Plant name (works for ANY plant globally)")
    parser.add_argument("--local-pdfs", nargs="+", help="Local PDF files to process")
    parser.add_argument("--years", nargs="+", type=int, help="Specific years to process (e.g., 2020 2021 2022)")
    
    args = parser.parse_args()
    
    print("🚀 COMPLETE INTEGRATED FINANCIAL PROCESSING PIPELINE")
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} UTC")
    print(f"👤 User: Gangatharan G")
    print(f"🏭 Plant: {args.plant_name}")
    print(f"🌍 Capabilities: ANY plant worldwide!")
    print(f"📆 Default Years: 2020-2024 (5 years)")
    print(f"📊 Outputs: 2 JSON files (Multi-year + Assumptions)")
    
    # Check environment
    required_vars = ["GEMINI_API_KEY", "OPENAI_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        print("\nRequired setup:")
        for var in missing_vars:
            print(f"export {var}='your_api_key_here'")
        return
    
    try:
        # Initialize complete pipeline
        pipeline = CompleteIntegratedPipeline()
        
        # Run complete integrated processing
        financial_path, assumptions_path, results = asyncio.run(
            pipeline.process_complete_pipeline(
                plant_name=args.plant_name,
                local_pdfs=args.local_pdfs,
                target_years=args.years
            )
        )
        
        if financial_path:
            print(f"\n✅ SUCCESS! Complete pipeline finished!")
            print(f"📄 Multi-Year Financial: {financial_path}")
            if assumptions_path:
                print(f"📄 Financial Assumptions: {assumptions_path}")
            else:
                print(f"📄 Financial Assumptions: Not generated")
        else:
            print(f"\n❌ Complete pipeline failed!")
            if 'error' in results:
                print(f"Error: {results['error']}")
        
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        logger.error(f"Fatal error: {e}")

if __name__ == "__main__":
    main()