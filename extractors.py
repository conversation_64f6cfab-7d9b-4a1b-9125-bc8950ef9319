"""
Focused Financial Data Extractor - Separate Balance Sheet & Profit Loss
Author: Gangatharangurusamy
Description: Extract financial data with focused prompts per statement type
Model: GPT-4o for precise extraction
Strategy: Separate extraction, then combine results
"""

import logging
import json
import re
from typing import Dict, Any, List, Optional
from datetime import datetime
import base64
import os

# Required imports
try:
    from openai import OpenAI
    from PIL import Image
except ImportError as e:
    print(f"Missing required library: {e}")
    print("Install with: pip install openai pillow")
    exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FocusedFinancialExtractor:
    """
    Focused extractor with separate prompts for Balance Sheet and Profit & Loss
    """
    
    # Balance Sheet focused prompt - ULTRA-ENHANCED FOR UK
    BALANCE_SHEET_PROMPT = """
    🇬🇧 CRITICAL: This is a UK company Balance Sheet. Look for £ symbols everywhere!
    
    You are extracting ONLY Balance Sheet data from UK financial statement images.

    🚨 UK MULTIPLIER RULES (MOST IMPORTANT):
    - "£000" or "£'000" → multiply by 1,000 (e.g., 1,234 becomes 1,234,000)
    - "£m" or "£ million" → multiply by 1,000,000 (e.g., 1.2 becomes 1,200,000)
    - "£" with no multiplier → use number as-is
    - If document shows "in millions" → multiply by 1,000,000
    - If document shows "in thousands" → multiply by 1,000
    - If no multiplier mentioned, use the raw number as-is

    🔍 CURRENCY & COUNTRY DETECTION (CRITICAL):
    - "Rs", "₹", "INR" → Currency: "INR", Country: "India"
    - "R", "Rm", "ZAR" → Currency: "ZAR", Country: "South Africa"  
    - "$", "USD" → Currency: "USD", Country: "United States"
    - "€", "EUR" → Currency: "EUR", Country: "Germany"
    - "£", "GBP", "£000", "£'000", "£m", "£ thousand", "£ million" → Currency: "GBP", Country: "United Kingdom"
    
    ⚠️ SPECIAL UK DETECTION RULES:
    - If you see ANY £ symbol anywhere in the document → Currency: "GBP", Country: "United Kingdom"
    - UK documents often show "£000" meaning "in thousands of pounds"
    - Look for company names ending in "Ltd", "Limited", "plc" → likely UK

    📊 EXTRACT BALANCE SHEET DATA ONLY:

    Look for these exact items in the Balance Sheet:
    - Company/Plant name from the header
    - Property, plant and equipment (PPE) / Fixed Assets / Non-current assets / Tangible fixed assets
    - Intangible assets / Intangible fixed assets
    - Current assets
    - Cash and cash equivalents / Cash at bank and in hand
    - Current liabilities
    - Short-term borrowings / Short-term debt / Creditors: amounts falling due within one year
    - Long-term borrowings / Long-term debt / Non-current liabilities / Creditors: amounts falling due after more than one year
    - Interest bearing loans and borrowings
    - Total equity / Shareholders' equity / Capital and reserves
    - Note references (e.g., "Note 8", "Note 14" - extract ONLY the number, not the word "Note")
    - Financial year (e.g., 2023, 2024, 2025)
    
    🇬🇧 UK-SPECIFIC TERMS TO LOOK FOR (CRITICAL):
    - "Tangible fixed assets" = Property, plant and equipment
    - "Intangible fixed assets" = Intangible assets
    - "Creditors: amounts falling due within one year" = Current liabilities + Short-term borrowings
    - "Creditors: amounts falling due after more than one year" = Long-term borrowings
    - "Capital and reserves" = Total equity
    - "Called up share capital" = Part of equity
    - "Profit and loss account" = Retained earnings (part of equity)
    
    🔍 EXTRACTION STRATEGY FOR UK BALANCE SHEETS:
    1. FIRST: Look for ANY £ symbol → immediately set Currency: "GBP", Country: "United Kingdom"
    2. SECOND: Look for "Tangible fixed assets" line → this is your PPE
    3. THIRD: Look for note numbers next to items (e.g., "Tangible fixed assets 14" → extract "14")
    4. FOURTH: Look for year in header (e.g., "31 December 2024" → extract "2024")
    5. FIFTH: Apply £000 multiplier if you see it anywhere

    🎯 REQUIRED JSON OUTPUT:
    ```json
    {
      "extraction_metadata": {
        "statement_type": "balance_sheet",
        "extraction_timestamp": "2025-07-02T11:19:56Z",
        "model_used": "gpt-4o",
        "multiplier_applied": true
      },
      "balance_sheet": {
        "country": "",
        "currency": "",
        "multiplier_detected": "",
        "plant_name": "",
        "financial_year": "",
        "Property, plant and equipment": "",
        "intangible_assets": "",
        "current_assets": "",
        "cash_and_cash_equivalents": "",
        "current_liabilities": "",
        "short_term_borrowings": "",
        "Long_Term_borrowing": "",
        "interest_bearing_loans_borrowings": "",
        "total_equity": "",
        "ppe_note_reference": "",
        "intangible_assets_note_reference": "",
        "borrowings_note_reference": ""
      }
    }
    ```

    🚨 ULTRA-CRITICAL INSTRUCTIONS FOR UK BALANCE SHEET:
    
    STEP 1 - CURRENCY DETECTION (MANDATORY):
    - Scan the ENTIRE image for ANY £ symbol
    - If you find £ ANYWHERE → Currency: "GBP", Country: "United Kingdom"
    - This is NON-NEGOTIABLE - £ = UK company
    
    STEP 2 - MULTIPLIER DETECTION (MANDATORY):
    - Look for "£000", "£'000" at top of columns → multiply ALL numbers by 1,000
    - Look for "£m" → multiply ALL numbers by 1,000,000
    - This affects ALL numerical values
    
    STEP 3 - PPE EXTRACTION (CRITICAL):
    - Look for "Tangible fixed assets" → this is Property, plant and equipment
    - Look for "Fixed assets" → this might be PPE
    - Extract the number and apply multiplier
    
    STEP 4 - NOTE REFERENCES (CRITICAL):
    - Look for numbers next to line items (e.g., "Tangible fixed assets 14")
    - Extract ONLY the number: "14" not "Note 14"
    - Common notes: 14, 15, 16, 17, 18, 19, 20
    
    STEP 5 - YEAR EXTRACTION (CRITICAL):
    - Look for dates like "31 December 2024" → extract "2024"
    - Look for "Year ended 2024" → extract "2024"
    
    STEP 6 - FINAL VALIDATION:
    - If you found £ symbols but still have "not_found" for currency → YOU MADE AN ERROR
    - If you found numbers but still have 0 for PPE → YOU MADE AN ERROR
    - DOUBLE-CHECK your work
    
    7. Return ONLY the JSON, no explanations
    8. All numbers should be AFTER multiplier application
    9. If numerical data truly not found, set to 0
    10. If text data truly not found, set to "not_found"
    11. Consider only standalone balance sheet, not consolidated

    """

    # Profit & Loss focused prompt  
    PROFIT_LOSS_PROMPT = """
    You are extracting ONLY Profit & Loss data from financial statement images.

    🚨 CRITICAL MULTIPLIER RULES:
    - If document shows "in millions" or "₹ million" or "Rm millions" → multiply by 1,000,000 (10^6)
    - If document shows "in billions" or "₹ billion" or "Rm billions" → multiply by 1,000,000,000 (10^9)  
    - If document shows "in thousands" or "₹ thousand" or "Rm thousands" → multiply by 1,000 (10^3)
    - If document shows "in crores" → multiply by 10,000,000 (10^7)
    - If document shows "in lakhs" → multiply by 100,000 (10^5)
    - If no multiplier mentioned, use the raw number as-is

    📈 EXTRACT PROFIT & LOSS DATA ONLY:

    Look for these exact items in the Profit & Loss Statement:
    - Revenue / Turnover / Sales (PRIORITY: Look for "Turnover" first)
    - Total income / Total revenue
    - Finance costs / Interest expense / Financial expenses / Interest payable and similar expenses
    - Profit before tax / Profit before interest and tax
    - Net profit / Net income / Profit after tax
    - Note references for finance costs and tax

    🎯 REQUIRED JSON OUTPUT:
    ```json
    {
      "extraction_metadata": {
        "statement_type": "profit_loss", 
        "extraction_timestamp": "2025-07-02T11:19:56Z",
        "model_used": "gpt-4o",
        "multiplier_applied": true
      },
      "profit_loss": {
        "multiplier_detected": "",
        "revenue": "",
        "turnover": "",
        "finance_cost": "",
        "interest_payable_similar_expenses": "",
        "profit_before_tax": "",
        "net_income": "",
        "financial_cost_note": [""],
        "tax_note_references": [""]
      }
    }
    ```

    🚨 INSTRUCTIONS:
    1. ONLY extract Profit & Loss data - ignore Balance Sheet items
    2. Apply multipliers to get final numerical values  
    3. Handle negative numbers correctly (losses)
    4. Extract note references exactly as shown
    5. Look for "Turnover" specifically and extract it separately
    6. Look for "Interest payable and similar expenses" separately from finance_cost
    
    🔍 CRITICAL: FINANCE COST EXTRACTION PRIORITY:
    - FIRST: Look for "Interest payable and similar expenses" → extract to interest_payable_similar_expenses
    - SECOND: Look for "Finance costs" or "Financial expenses" → extract to finance_cost
    - THIRD: Look for "Interest expense" → extract to finance_cost
    - If you find "Interest payable and similar expenses", make sure to extract the NUMBER, not just text!
    7. Return ONLY the JSON, no explanations
    8. All numbers should be AFTER multiplier application
    9. If data is not found, set the value to 0 for numerical fields
    10. If data is not found, then put "not_found" for text fields
    11. Please consider only stand alone profit and loss statement and not consolidated profit and loss statement



    """

    def __init__(self):
        """Initialize the focused extractor with GPT-4o"""
        # Check for OpenAI API key
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY not found. Please set it in .env file")
        
        # Initialize OpenAI client
        self.client = OpenAI(api_key=api_key)
        
        logger.info("✅ Focused Financial Extractor initialized with GPT-4o")
        logger.info("🎯 Strategy: Separate extraction for Balance Sheet and Profit & Loss")

    def encode_image_base64(self, image_path: str) -> str:
        """Encode image to base64 for OpenAI API"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def extract_balance_sheet(self, balance_sheet_images: List[str]) -> Dict[str, Any]:
        """
        Extract ONLY Balance Sheet data using focused prompt
        """
        logger.info("📊 Extracting Balance Sheet data with focused prompt...")
        logger.info(f"📁 Balance Sheet images provided: {len(balance_sheet_images)}")
        for i, img_path in enumerate(balance_sheet_images):
            logger.info(f"  📄 Image {i+1}: {img_path}")
        
        try:
            # Prepare content for Balance Sheet extraction
            content = [
                {
                    "type": "text",
                    "text": self.BALANCE_SHEET_PROMPT + f"""
                    
🚨 CRITICAL: YOU ARE PROCESSING {len(balance_sheet_images)} BALANCE SHEET IMAGES!

⚠️ IMPORTANT INSTRUCTIONS:
1. Look at ALL images provided - some may have summary, others detailed data
2. If you see ACTUAL NUMBERS (not just zeros), extract them and apply multipliers
3. If you see £000 or £'000 anywhere, set multiplier_detected to "£000"
4. If you see note numbers (like "14", "15"), extract them
5. DO NOT return string "0" - return numeric 0 if no data found
6. LOOK CAREFULLY for "Tangible fixed assets" or "Fixed assets" lines with actual values

🔍 EXPECTED: You should find REAL numbers, not all zeros!

🚨 SPECIFIC THINGS TO LOOK FOR IN UK BALANCE SHEETS:
- "Tangible fixed assets" with a number next to it (this is PPE)
- "£000" or "£'000" at the top of columns (this is the multiplier)
- "Capital and reserves" with a number (this is total equity)
- "Creditors: amounts falling due within one year" (current liabilities)
- "Creditors: amounts falling due after more than one year" (long-term debt)
- Note numbers like "14", "15", "16" next to line items

🎯 CRITICAL: If you see numbers like 1,234 or 5,678 in the images, extract them!
Don't return all zeros unless the Balance Sheet literally shows all zero values!
"""
                }
            ]
            
            # Add balance sheet images
            for i, image_path in enumerate(balance_sheet_images[:3]):  # Limit to 3 images
                image_base64 = self.encode_image_base64(image_path)
                
                content.append({
                    "type": "text",
                    "text": f"\n📊 Balance Sheet Image {i+1}:"
                })
                
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{image_base64}"
                    }
                })
            
            # Call GPT-4o for Balance Sheet extraction
            logger.info("🤖 Calling GPT-4o for Balance Sheet extraction...")
            
            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": content
                    }
                ],
                max_tokens=2000,
                temperature=0
            )
            
            # Parse response
            response_text = response.choices[0].message.content.strip()
            
            # Clean JSON response
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()
            # Debug logging (ENABLED for troubleshooting)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            raw_filename = f"gpt_raw_balance_sheet_{timestamp}.txt"
            with open(raw_filename, "w", encoding="utf-8") as f:
                f.write(response_text)
            logger.info(f"📝 Saved raw GPT Balance Sheet response to: {raw_filename}")
            
            # Log first 500 characters for debugging
            logger.info(f"🔍 GPT Balance Sheet Response Preview: {response_text[:500]}...")
            
            # Log response length
            logger.info(f"📏 GPT Response Length: {len(response_text)} characters")

            # Parse JSON
            balance_sheet_data = json.loads(response_text)
            
            # Post-process: Convert string numbers to actual numbers
            if "balance_sheet" in balance_sheet_data:
                bs_data = balance_sheet_data["balance_sheet"]
                numeric_fields = [
                    "Property, plant and equipment",
                    "intangible_assets", 
                    "current_assets",
                    "cash_and_cash_equivalents",
                    "current_liabilities",
                    "short_term_borrowings",
                    "Long_Term_borrowing", 
                    "interest_bearing_loans_borrowings",
                    "total_equity"
                ]
                
                for field in numeric_fields:
                    if field in bs_data:
                        value = bs_data[field]
                        if isinstance(value, str):
                            try:
                                # Try to convert string to number
                                if value.replace(",", "").replace(".", "").replace("-", "").isdigit():
                                    bs_data[field] = float(value.replace(",", ""))
                                    logger.info(f"🔄 Converted {field} from '{value}' to {bs_data[field]}")
                                elif value == "0":
                                    bs_data[field] = 0
                                    logger.info(f"🔄 Converted {field} from string '0' to numeric 0")
                            except (ValueError, AttributeError):
                                logger.warning(f"⚠️ Could not convert {field} value: {value}")
            
            logger.info("✅ Balance Sheet extraction completed successfully")
            self._log_balance_sheet_summary(balance_sheet_data)
            
            return balance_sheet_data
            
        except Exception as e:
            logger.error(f"❌ Balance Sheet extraction failed: {e}")
            raise Exception(f"Balance Sheet extraction failed: {e}")

    def extract_profit_loss(self, profit_loss_images: List[str]) -> Dict[str, Any]:
        """
        Extract ONLY Profit & Loss data using focused prompt
        """
        logger.info("📈 Extracting Profit & Loss data with focused prompt...")
        
        try:
            # Prepare content for Profit & Loss extraction
            content = [
                {
                    "type": "text",
                    "text": self.PROFIT_LOSS_PROMPT
                }
            ]
            
            # Add profit & loss images
            for i, image_path in enumerate(profit_loss_images[:3]):  # Limit to 3 images
                image_base64 = self.encode_image_base64(image_path)
                
                content.append({
                    "type": "text",
                    "text": f"\n📈 Profit & Loss Image {i+1}:"
                })
                
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{image_base64}"
                    }
                })
            
            # Call GPT-4o for Profit & Loss extraction
            logger.info("🤖 Calling GPT-4o for Profit & Loss extraction...")
            
            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": content
                    }
                ],
                max_tokens=2000,
                temperature=0
            )
            
            # Parse response
            response_text = response.choices[0].message.content.strip()
            
            # Clean JSON response
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()
            # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # raw_filename = f"gpt_raw_profit_loss_{timestamp}.txt"
            # with open(raw_filename, "w", encoding="utf-8") as f:
            #     f.write(response_text)
            # logger.info(f"📝 Saved raw GPT Profit & Loss response to: {raw_filename}")

                # Save raw GPT response to file for debugging         
            # Parse JSON
            profit_loss_data = json.loads(response_text)
            
            logger.info("✅ Profit & Loss extraction completed successfully")
            self._log_profit_loss_summary(profit_loss_data)
            
            return profit_loss_data
            
        except Exception as e:
            logger.error(f"❌ Profit & Loss extraction failed: {e}")
            raise Exception(f"Profit & Loss extraction failed: {e}")

    def extract_financial_data(self, classified_images: Dict[str, List[str]]) -> Dict[str, Any]:
        """
        Main extraction method - combines focused extractions (FIXED)
        """
        logger.info("🚀 Starting focused financial data extraction...")
        
        # DEBUG: Log all available classification categories
        logger.info(f"📂 Available classification categories: {list(classified_images.keys())}")
        for category, images in classified_images.items():
            logger.info(f"  📁 {category}: {len(images)} images")
        
        # FIXED: Initialize proper structure from the start
        extracted_data = {
            "extraction_metadata": {
                "extraction_timestamp": datetime.now().isoformat(),
                "extraction_strategy": "focused_separate_prompts",
                "model_used": "gpt-4o",
                "statements_processed": []
            }
        }
        
        # Extract Balance Sheet data - FLEXIBLE MATCHING
        balance_sheet_key = None
        possible_balance_sheet_keys = [
            "Statement of Balance Sheet",
            "Balance Sheet", 
            "Statement of Financial Position",
            "Financial Position",
            "balance_sheet",
            "Balance sheet"
        ]
        
        for key in possible_balance_sheet_keys:
            if key in classified_images:
                balance_sheet_key = key
                break
        
        if balance_sheet_key:
            logger.info(f"📊 Processing Balance Sheet images (found as: '{balance_sheet_key}')...")
            try:
                balance_sheet_result = self.extract_balance_sheet(
                    classified_images[balance_sheet_key]
                )
                
                # Extract only the balance_sheet data, not metadata
                if "balance_sheet" in balance_sheet_result:
                    extracted_data["balance_sheet"] = balance_sheet_result["balance_sheet"]
                    extracted_data["extraction_metadata"]["statements_processed"].append("balance_sheet")
                    logger.info("✅ Balance Sheet data merged successfully")
                else:
                    logger.warning("⚠️ No balance sheet data found in extraction result")
                    
            except Exception as e:
                logger.error(f"❌ Balance Sheet extraction failed: {e}")
                # Continue with other extractions
        else:
            logger.warning(f"⚠️ No Balance Sheet found in classified images")
            logger.warning(f"📋 Looked for: {possible_balance_sheet_keys}")
            logger.warning(f"📂 Available: {list(classified_images.keys())}")
        
        # Extract Profit & Loss data
        if "Statement of Profit and Loss" in classified_images:
            logger.info("📈 Processing Profit & Loss images...")
            try:
                profit_loss_result = self.extract_profit_loss(
                    classified_images["Statement of Profit and Loss"]
                )
                
                # Extract only the profit_loss data, not metadata
                if "profit_loss" in profit_loss_result:
                    extracted_data["profit_loss"] = profit_loss_result["profit_loss"]
                    extracted_data["extraction_metadata"]["statements_processed"].append("profit_loss")
                    logger.info("✅ Profit & Loss data merged successfully")
                else:
                    logger.warning("⚠️ No profit & loss data found in extraction result")
                    
            except Exception as e:
                logger.error(f"❌ Profit & Loss extraction failed: {e}")
                # Continue with pipeline
        
        # Check if we got any data
        if not extracted_data["extraction_metadata"]["statements_processed"]:
            raise Exception("No financial statements were successfully extracted")
        
        # Validate consistency between statements (only if we have both)
        if len(extracted_data["extraction_metadata"]["statements_processed"]) > 1:
            try:
                self._validate_consistency(extracted_data)
            except Exception as e:
                logger.warning(f"⚠️ Consistency validation failed: {e}")
        
        logger.info("🎉 Focused financial data extraction completed successfully")
        logger.info(f"📊 Statements processed: {extracted_data['extraction_metadata']['statements_processed']}")
        
        return extracted_data

    def _log_balance_sheet_summary(self, balance_sheet_data: Dict[str, Any]) -> None:
        """Log Balance Sheet extraction summary - ENHANCED DEBUG"""
        if "balance_sheet" in balance_sheet_data:
            bs = balance_sheet_data["balance_sheet"]
            logger.info("📊 BALANCE SHEET SUMMARY:")
            logger.info(f"  🏭 Company: {bs.get('plant_name', 'Unknown')}")
            logger.info(f"  🌍 Country: {bs.get('country', 'Unknown')}")
            logger.info(f"  💱 Currency: {bs.get('currency', 'Unknown')}")
            logger.info(f"  🔢 Multiplier: {bs.get('multiplier_detected', 'Unknown')}")
            logger.info(f"  📅 Financial Year: {bs.get('financial_year', 'Unknown')}")
            
            # Enhanced logging for all fields
            for field, label in [
                ("Property, plant and equipment", "🏗️ PPE"),
                ("total_equity", "💰 Total Equity"),
                ("short_term_borrowings", "💸 Short-term Borrowings"),
                ("Long_Term_borrowing", "🏦 Long-term Borrowings"),
                ("current_assets", "📦 Current Assets"),
                ("current_liabilities", "📋 Current Liabilities"),
                ("ppe_note_reference", "📝 PPE Note"),
                ("borrowings_note_reference", "📝 Borrowings Note"),
            ]:
                val = bs.get(field)
                if isinstance(val, (int, float)):
                    logger.info(f"  {label}: {val:,.0f}")
                elif val == "not_found":
                    logger.warning(f"  {label}: not_found")
                elif val == 0:
                    logger.warning(f"  {label}: 0 (extracted as zero)")
                else:
                    logger.warning(f"  {label}: {val} (type: {type(val)})")
            
            # Check for critical issues
            critical_issues = []
            if bs.get('country') == 'not_found':
                critical_issues.append("Country not detected")
            if bs.get('currency') == 'not_found':
                critical_issues.append("Currency not detected")
            if bs.get('Property, plant and equipment', 0) == 0:
                critical_issues.append("PPE is zero")
            if bs.get('multiplier_detected') == 'not_found':
                critical_issues.append("Multiplier not detected")
                
            if critical_issues:
                logger.error(f"🚨 CRITICAL BALANCE SHEET ISSUES: {', '.join(critical_issues)}")
                logger.error("🔍 This suggests GPT-4o is not reading the Balance Sheet image correctly")
        else:
            logger.error("❌ No 'balance_sheet' key found in extraction result")


    def _log_profit_loss_summary(self, profit_loss_data: Dict[str, Any]) -> None:
        """Log Profit & Loss extraction summary"""
        if "profit_loss" in profit_loss_data:
            pl = profit_loss_data["profit_loss"]
            logger.info("📈 PROFIT & LOSS SUMMARY:")
            logger.info(f"  🔢 Multiplier: {pl.get('multiplier_detected', 'Unknown')}")

            for field, label in [
                ("revenue", "💰 Revenue"),
                ("finance_cost", "💸 Finance Cost"),
                ("profit_before_tax", "📊 Profit Before Tax"),
                ("net_income", "📈 Net Income"),
            ]:
                val = pl.get(field)
                if isinstance(val, (int, float)):
                    logger.info(f"  {label}: {val:,.0f}")
                elif val == "not_found":
                    logger.warning(f"  {label} explicitly marked as not found")
                else:
                    logger.warning(f"  {label} missing or invalid (got {val})")

    def _validate_consistency(self, extracted_data: Dict[str, Any]) -> None:
        """Validate consistency between Balance Sheet and Profit & Loss"""
        logger.info("🔍 Validating consistency between statements...")

        # Check if both statements use same multiplier
        bs_multiplier = extracted_data.get("balance_sheet", {}).get("multiplier_detected")
        pl_multiplier = extracted_data.get("profit_loss", {}).get("multiplier_detected")

        if bs_multiplier and pl_multiplier and bs_multiplier != pl_multiplier:
            logger.warning(f"⚠️ Multiplier mismatch: Balance Sheet uses {bs_multiplier}, P&L uses {pl_multiplier}")

        # Check for reasonable values - FIXED: Handle string values safely
        equity = extracted_data.get("balance_sheet", {}).get("total_equity", 0)
        net_income = extracted_data.get("profit_loss", {}).get("net_income", 0)

        # Convert to numeric values safely
        try:
            equity_num = self._safe_numeric_conversion(equity)
            net_income_num = self._safe_numeric_conversion(net_income)

            if equity_num > 0 and net_income_num != 0:
                roe = net_income_num / equity_num
                if abs(roe) > 1.0:  # ROE > 100%
                    logger.warning(f"⚠️ Unusual ROE detected: {roe:.1%}")
        except Exception as e:
            logger.warning(f"⚠️ Could not validate ROE: {e}")

        logger.info("✅ Consistency validation completed")

    def _safe_numeric_conversion(self, value) -> float:
        """Safely convert value to numeric, handling strings and other types"""
        if value is None:
            return 0.0

        if isinstance(value, (int, float)):
            return float(value)

        if isinstance(value, str):
            # Handle special cases
            if value.lower() in ['not_found', 'n/a', 'na', '-', '']:
                return 0.0

            try:
                # Remove common non-numeric characters
                clean_value = value.replace(',', '').replace('(', '-').replace(')', '')
                clean_value = ''.join(c for c in clean_value if c.isdigit() or c in '.-')
                return float(clean_value) if clean_value else 0.0
            except (ValueError, TypeError):
                return 0.0

        return 0.0

    def extract_from_classification_manifest(self, manifest_path: str) -> Dict[str, Any]:
        """
        Extract financial data using classification manifest
        """
        logger.info(f"📋 Loading classification manifest: {manifest_path}")
        
        try:
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest = json.load(f)
            
            # Get classified images from manifest
            classified_images = {}
            
            if "classifications" in manifest:
                for classification, details in manifest["classifications"].items():
                    if "full_paths" in details:
                        classified_images[classification] = details["full_paths"]
            
            logger.info(f"📂 Found {len(classified_images)} classification categories")
            for category, images in classified_images.items():
                logger.info(f"  📁 {category}: {len(images)} images")
            
            # Extract financial data using focused approach
            return self.extract_financial_data(classified_images)
            
        except Exception as e:
            logger.error(f"❌ Failed to load manifest: {e}")
            raise

    def extract_notes_data(self, notes_images: List[str], note_references: Dict[str, str], 
                          financial_year: str) -> Dict[str, Any]:
        """
        Extract specific data from notes based on note references
        """
        logger.info("📝 Extracting data from notes...")
        
        if not notes_images:
            logger.warning("⚠️ No notes images provided")
            return {}
        
        try:
            # DEBUG: Log the prompt being sent
            logger.info(f"🔍 DEBUG: Notes extraction prompt will look for:")
            logger.info(f"  📊 PPE Note: {note_references.get('ppe_note_reference', 'X')}")
            logger.info(f"  📊 Borrowings Note: {note_references.get('borrowings_note_reference', 'X')}")
            logger.info(f"  📊 Financial Year: {financial_year}")
            
            # Create notes extraction prompt
            notes_prompt = f"""
            You are extracting specific financial data from Notes to Financial Statements.

            🎯 EXTRACT THE FOLLOWING DATA:

            1. **PPE/Tangible Assets Details** (from Note {note_references.get('ppe_note_reference', 'X')}):
               - Look for "Property, plant and equipment" or "Tangible fixed assets"
               - Find "Cost or valuation" section
               - Extract the TOTAL for year {financial_year}

            2. **Intangible Assets Details** (from Note {note_references.get('intangible_assets_note_reference', 'X')}):
               - Look for "Intangible assets" 
               - Find "Cost or valuation" section
               - Extract the TOTAL for year {financial_year}

            3. **Borrowings Details** (from Note {note_references.get('borrowings_note_reference', 'X')}):
               - Look for "Interest bearing loans and borrowings" or "Borrowings"
               - Extract short-term borrowings amount
               - Extract long-term borrowings amount
               - Extract total borrowings

            🎯 REQUIRED JSON OUTPUT:
            ```json
            {{
              "extraction_metadata": {{
                "statement_type": "notes_extraction",
                "extraction_timestamp": "{datetime.now().isoformat()}",
                "model_used": "gpt-4o",
                "financial_year": "{financial_year}"
              }},
              "notes_data": {{
                "ppe_cost_valuation_total": "",
                "intangible_assets_cost_valuation_total": "",
                "short_term_borrowings_detail": "",
                "long_term_borrowings_detail": "",
                "total_borrowings_detail": "",
                "notes_found": []
              }}
            }}
            ```

            🚨 CRITICAL INSTRUCTIONS - SEARCH AGGRESSIVELY:
            
            **STEP 1: FIND THE NOTES**
            - Scan ALL images for "Note {note_references.get('ppe_note_reference', 'X')}" or "{note_references.get('ppe_note_reference', 'X')}." anywhere on the page
            - Scan ALL images for "Note {note_references.get('borrowings_note_reference', 'X')}" or "{note_references.get('borrowings_note_reference', 'X')}." anywhere on the page
            - Look for variations: "Note {note_references.get('ppe_note_reference', 'X')}:", "{note_references.get('ppe_note_reference', 'X')} Property", "{note_references.get('ppe_note_reference', 'X')} Tangible", etc.
            
            **STEP 2: EXTRACT DATA**
            - **PPE (Note {note_references.get('ppe_note_reference', 'X')})**: Look for ANY table with "Cost", "Valuation", "Gross", or "Total" columns
            - **Borrowings (Note {note_references.get('borrowings_note_reference', 'X')})**: Look for ANY borrowings amounts, loan details, or debt breakdown
            
            **STEP 3: HANDLE MULTIPLIERS**
            - If you see "£'000", "£000", or "(£'000)", multiply ALL numbers by 1000
            - If you see "£m" or "£million", multiply by 1,000,000
            
            **STEP 4: RETURN RESULTS**
            - Extract EXACT numerical values (no commas, no currency symbols)
            - In "notes_found", list ALL note numbers you found (even if partially)
            - If you find ANY relevant data, include it even if incomplete
            - JSON ONLY - no explanations
            """

            # Prepare content for notes extraction
            content = [
                {
                    "type": "text",
                    "text": notes_prompt
                }
            ]
            
            # Add notes images (limit to 5 images to avoid token limits)
            for i, image_path in enumerate(notes_images[:5]):
                image_base64 = self.encode_image_base64(image_path)
                
                content.append({
                    "type": "text",
                    "text": f"\n📝 Notes Image {i+1}:"
                })
                
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{image_base64}"
                    }
                })
            
            # Call GPT-4o for notes extraction
            logger.info("🤖 Calling GPT-4o for notes extraction...")
            
            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": content
                    }
                ],
                max_tokens=2000,
                temperature=0
            )
            
            # Parse response
            response_text = response.choices[0].message.content.strip()
            
            # Clean JSON response
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()
            
            # DEBUG: Log the raw LLM response for notes extraction
            logger.info(f"🔍 DEBUG: Raw LLM notes response (first 500 chars): {response_text[:500]}")
            
            # Parse JSON
            notes_data = json.loads(response_text)
            
            # DEBUG: Log what was actually extracted from notes
            if "notes_data" in notes_data:
                extracted_notes = notes_data["notes_data"]
                logger.info(f"🔍 DEBUG: Notes extraction results:")
                logger.info(f"  📊 PPE Cost/Valuation Total: {extracted_notes.get('ppe_cost_valuation_total', 'NOT_FOUND')}")
                logger.info(f"  📊 Short-term Borrowings Detail: {extracted_notes.get('short_term_borrowings_detail', 'NOT_FOUND')}")
                logger.info(f"  📊 Long-term Borrowings Detail: {extracted_notes.get('long_term_borrowings_detail', 'NOT_FOUND')}")
                logger.info(f"  📊 Notes Found: {extracted_notes.get('notes_found', [])}")
            else:
                logger.warning(f"⚠️ No 'notes_data' key in extracted data: {list(notes_data.keys())}")
            
            logger.info("✅ Notes extraction completed successfully")
            return notes_data
            
        except Exception as e:
            logger.error(f"❌ Notes extraction failed: {e}")
            return {}

    def extract_financial_data_enhanced(self, classified_images: Dict[str, List[str]]) -> Dict[str, Any]:
        """
        Enhanced extraction with two-stage approach: primary data + notes details
        """
        logger.info("🚀 Starting enhanced two-stage financial extraction...")
        logger.info(f"🔍 ENHANCED EXTRACTION CALLED - Available categories: {list(classified_images.keys())}")
        
        # FORCE DEBUG: Write to file to confirm this method is being called
        try:
            with open("ENHANCED_EXTRACTION_CALLED.txt", "w") as f:
                f.write(f"Enhanced extraction called at {datetime.now().isoformat()}\n")
                f.write(f"Available categories: {list(classified_images.keys())}\n")
        except:
            pass
        
        try:
            # Stage 1: Extract primary data from Balance Sheet and P&L
            primary_data = self.extract_financial_data(classified_images)
            
            # DEBUG: Log the actual structure of primary_data
            logger.info(f"🔍 DEBUG: Primary data keys: {list(primary_data.keys())}")
            if "balance_sheet" in primary_data:
                logger.info(f"🔍 DEBUG: Balance sheet keys: {list(primary_data['balance_sheet'].keys())}")
            else:
                logger.warning(f"🔍 DEBUG: No 'balance_sheet' key found in primary_data")
            
            # Stage 2: Extract notes data if notes are available - FLEXIBLE MATCHING
            notes_data = {}
            notes_key = None
            possible_notes_keys = [
                "Notes to Financial Statements",
                "Notes to the Financial Statements", 
                "Notes",
                "Financial Statement Notes",
                "notes"
            ]
            
            for key in possible_notes_keys:
                if key in classified_images:
                    notes_key = key
                    break
            
            if notes_key:
                notes_images = classified_images[notes_key]
                logger.info(f"📝 Found notes images under category: '{notes_key}' ({len(notes_images)} images)")
                
                # Get note references from primary extraction - ROBUST MAPPING
                bs_data = {}
                
                # Try multiple possible data structures
                if "balance_sheet" in primary_data:
                    bs_data = primary_data["balance_sheet"]
                elif "balance_sheet_data" in primary_data and "balance_sheet" in primary_data["balance_sheet_data"]:
                    bs_data = primary_data["balance_sheet_data"]["balance_sheet"]
                else:
                    logger.warning("⚠️ Could not find balance sheet data for note references")
                    logger.info(f"🔍 Available keys: {list(primary_data.keys())}")
                
                note_references = {
                    "ppe_note_reference": bs_data.get("ppe_note_reference", ""),
                    "intangible_assets_note_reference": bs_data.get("intangible_assets_note_reference", ""),
                    "borrowings_note_reference": bs_data.get("borrowings_note_reference", "")
                }
                
                logger.info(f"📝 Note references found: {note_references}")
                logger.info(f"📝 Balance sheet data available: {bool(bs_data)}")
                
                # Get financial year BEFORE using it in logging
                financial_year = bs_data.get("financial_year", "2024")
                
                # DEBUG: Log specific note references
                logger.info(f"🔍 PPE Note Reference: '{note_references.get('ppe_note_reference', 'NOT_FOUND')}'")
                logger.info(f"🔍 Borrowings Note Reference: '{note_references.get('borrowings_note_reference', 'NOT_FOUND')}'")
                logger.info(f"🔍 Financial Year: '{financial_year}'")
                
                # Extract notes data
                logger.info(f"🚀 Starting notes extraction with {len(notes_images)} images...")
                try:
                    notes_data = self.extract_notes_data(notes_images, note_references, financial_year)
                    logger.info(f"✅ Notes extraction completed successfully")
                    if notes_data and "notes_data" in notes_data:
                        logger.info(f"📊 Notes data extracted: {list(notes_data['notes_data'].keys())}")
                    else:
                        logger.warning(f"⚠️ Notes extraction returned empty or invalid data")
                except Exception as e:
                    logger.error(f"❌ Notes extraction failed: {e}")
                    notes_data = {}
            else:
                logger.warning("⚠️ No notes images found for enhanced extraction")
                logger.info(f"📋 Looked for: {possible_notes_keys}")
                logger.info(f"📂 Available: {list(classified_images.keys())}")
            
            # Combine primary and notes data
            enhanced_data = {
                **primary_data,
                "notes_data": notes_data,
                "extraction_type": "enhanced_two_stage"
            }
            
            logger.info("✅ Enhanced two-stage extraction completed successfully")
            return enhanced_data
            
        except Exception as e:
            logger.error(f"❌ Enhanced extraction failed: {e}")
            # Fallback to primary extraction only
            return self.extract_financial_data(classified_images)

    def extract_profit_loss_only(self, profit_loss_images: List[str]) -> Dict[str, Any]:
        """
        Extract ONLY net income from P&L images - NO CURRENCY CONVERSION
        Used specifically for ROE calculation during year processing
        Returns raw values without multipliers for unit consistency
        """
        logger.info("💰 Starting P&L-only extraction for ROE calculation (raw values)...")
        logger.info(f"📁 P&L images provided: {len(profit_loss_images)}")
        
        if not profit_loss_images:
            logger.warning("⚠️ No P&L images provided")
            return {"net_income": 0}
        
        try:
            # Prepare content for P&L-only extraction
            content = [
                {
                    "type": "text", 
                    "text": f"""
🎯 EXTRACT ONLY NET INCOME FROM PROFIT & LOSS STATEMENT

CRITICAL: Return RAW VALUES - NO CURRENCY CONVERSION OR MULTIPLICATION!

You are extracting ONLY net income from Profit & Loss statement images.
This is for ROE calculation where units must be consistent.

📊 EXTRACT ONLY:
- Net profit / Net income / Profit after tax (RAW NUMBER ONLY)

🚨 CRITICAL RULES:
1. DO NOT apply any multipliers (no ×1000, ×1,000,000)
2. Extract the RAW number as shown in the document
3. If you see "1,234" in the document, return 1234 (not 1,234,000)
4. If you see negative values (losses), include the negative sign
5. Return ONLY the numerical value

🎯 REQUIRED JSON OUTPUT:
```json
{{
  "net_income": [RAW_NUMBER_HERE]
}}
```

Examples:
- Document shows "1,234" → Return: {{"net_income": 1234}}
- Document shows "(567)" → Return: {{"net_income": -567}}
- Document shows "2,345,678" → Return: {{"net_income": 2345678}}

IMPORTANT: Return ONLY the JSON, no explanations.
Extract the raw number exactly as shown, without any multiplier application.
"""
                }
            ]
            
            # Add P&L images (limit to 3 for API efficiency)
            for i, image_path in enumerate(profit_loss_images[:3]):
                image_base64 = self.encode_image_base64(image_path)
                
                content.append({
                    "type": "text",
                    "text": f"\n💰 P&L Image {i+1}:"
                })
                
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{image_base64}"
                    }
                })
            
            # Call GPT-4o for P&L-only extraction
            logger.info("🤖 Calling GPT-4o for P&L-only extraction...")
            
            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": content
                    }
                ],
                max_tokens=500,  # Smaller since we only need net income
                temperature=0
            )
            
            # Parse response
            response_text = response.choices[0].message.content.strip()
            
            # Clean JSON response
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()
            
            # Debug logging
            logger.info(f"🔍 P&L-only Response: {response_text}")
            
            # Parse JSON
            pl_data = json.loads(response_text)
            
            # Extract net income and ensure it's numeric
            net_income = pl_data.get("net_income", 0)
            
            # Convert to float if it's a string
            if isinstance(net_income, str):
                try:
                    # Clean string and convert to number
                    cleaned = net_income.replace(",", "").replace("(", "-").replace(")", "")
                    net_income = float(cleaned)
                except:
                    logger.warning(f"⚠️ Could not convert net_income '{net_income}' to number, using 0")
                    net_income = 0
            
            logger.info(f"💰 Net Income extracted (raw): {net_income}")
            logger.info("✅ P&L-only extraction completed successfully")
            
            return {"net_income": net_income}
            
        except Exception as e:
            logger.error(f"❌ P&L-only extraction failed: {e}")
            return {"net_income": 0}


# Test function
def test_focused_extractor():
    """Test the focused financial extractor"""
    print("🧪 Testing Focused Financial Extractor...")
    
    extractor = FocusedFinancialExtractor()
    
    print("\n✅ Focused Financial Extractor test completed!")
    print("🎯 Ready for separate Balance Sheet and Profit & Loss extraction!")


if __name__ == "__main__":
    test_focused_extractor()