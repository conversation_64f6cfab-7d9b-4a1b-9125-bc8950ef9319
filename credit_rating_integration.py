"""
Credit Rating Integration Module
Date: 2025-01-11
User: Gangatharan G
Purpose: Integrate credit rating research agent with the existing pipeline
"""

import os
import json
import sys
import logging
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
from langchain_core.messages import HumanMessage

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.agent.credit_rating_graph import enhanced_graph
    from src.agent.tools_and_schemas import CreditRatingResponse
    AGENT_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Credit rating agent not available: {e}")
    enhanced_graph = None
    AGENT_AVAILABLE = False

# Configure logging
logger = logging.getLogger(__name__)

class CreditRatingIntegrator:
    """Integrates credit rating research agent with the existing pipeline"""
    
    def __init__(self):
        self.agent_available = AGENT_AVAILABLE
        if not self.agent_available:
            logger.warning("⚠️ Credit rating agent not available - integration disabled")
        else:
            logger.info("✅ Credit rating agent initialized successfully")
    
    def check_credit_rating_completeness(self, financial_data: Dict) -> Dict[str, Any]:
        """
        Check if credit rating data is missing or incomplete
        
        Args:
            financial_data: The financial details JSON data
            
        Returns:
            Dict with completeness analysis
        """
        analysis = {
            "has_credit_rating_field": False,
            "is_empty": True,
            "is_incomplete": False,
            "missing_agencies": [],
            "incomplete_years": [],
            "needs_research": False,
            "reason": ""
        }
        
        # Check if credit_rating field exists
        if "credit_rating" not in financial_data:
            analysis.update({
                "needs_research": True,
                "reason": "credit_rating field is missing entirely"
            })
            return analysis
        
        analysis["has_credit_rating_field"] = True
        credit_rating = financial_data["credit_rating"]
        
        # Check if empty list
        if not credit_rating or len(credit_rating) == 0:
            analysis.update({
                "is_empty": True,
                "needs_research": True,
                "reason": "credit_rating field is empty array"
            })
            return analysis
        
        analysis["is_empty"] = False
        
        # Check for incomplete data (empty strings, missing data)
        incomplete_agencies = []
        for i, agency in enumerate(credit_rating):
            agency_name = agency.get("agency", "")
            name = agency.get("name", "")
            yearwise_rating = agency.get("yearwise_rating", [])
            
            # Check if agency info is empty
            if not agency_name and not name:
                incomplete_agencies.append(f"Agency {i+1}: both agency and name are empty")
                continue
            
            # Check if yearwise ratings are empty or incomplete
            if not yearwise_rating:
                incomplete_agencies.append(f"{agency_name or name}: no yearwise_rating data")
                continue
            
            # Check for empty rating values
            empty_ratings = []
            for j, rating in enumerate(yearwise_rating):
                rating_value = rating.get("rating", "")
                rating_trunc = rating.get("rating_trunc", "")
                year = rating.get("year", "")
                
                if not rating_value or not year:
                    empty_ratings.append(f"entry {j+1} (year: {year or 'empty'})")
            
            if empty_ratings:
                incomplete_agencies.append(f"{agency_name or name}: empty ratings in {', '.join(empty_ratings)}")
        
        if incomplete_agencies:
            analysis.update({
                "is_incomplete": True,
                "missing_agencies": incomplete_agencies,
                "needs_research": True,
                "reason": f"Incomplete credit rating data: {'; '.join(incomplete_agencies)}"
            })
        else:
            analysis.update({
                "needs_research": False,
                "reason": "Credit rating data appears complete"
            })
        
        return analysis
    
    def extract_plant_name_from_data(self, financial_data: Dict) -> str:
        """Extract plant name from financial data"""
        # Try multiple possible fields
        possible_fields = ["plant_name", "name", "entity_name", "company_name"]
        
        for field in possible_fields:
            if field in financial_data and financial_data[field]:
                return financial_data[field]
        
        # Fallback: try to extract from pk or other fields
        pk = financial_data.get("pk", "")
        if pk:
            return f"Plant {pk}"
        
        return "Unknown Power Plant"
    
    async def research_credit_rating(self, plant_name: str, country_name: str) -> Optional[Dict]:
        """
        Research credit rating for a power plant using the agent
        
        Args:
            plant_name: Name of the power plant
            country_name: Country where the plant is located
            
        Returns:
            Credit rating data or None if research fails
        """
        if not self.agent_available:
            logger.warning("⚠️ Credit rating agent not available - skipping research")
            return None
        
        try:
            logger.info(f"🔍 Starting credit rating research for: {plant_name} in {country_name}")
            
            # Construct research query in the exact format expected by the agent
            research_query = f"Credit rating of {plant_name} {country_name}"
            
            # Prepare the state for the agent
            state = {
                "messages": [HumanMessage(content=research_query)],
                "initial_search_query_count": 3,
                "max_research_loops": 2,
                "reasoning_model": "gemini-2.5-flash",
            }
            
            logger.info(f"🤖 Executing credit rating research agent with query: '{research_query}'")
            
            # Execute the enhanced graph
            result = enhanced_graph.invoke(state)
            
            # Debug: Log the result structure
            logger.info(f"🔍 Agent result keys: {list(result.keys()) if result else 'None'}")
            
            # Extract credit rating result
            credit_rating_result = result.get("credit_rating_result") if result else None
            
            if credit_rating_result and not credit_rating_result.get("error"):
                logger.info(f"✅ Credit rating research successful for {plant_name}")
                
                # Log what we found
                if "credit_rating" in credit_rating_result and credit_rating_result["credit_rating"]:
                    agencies_count = len(credit_rating_result["credit_rating"])
                    logger.info(f"📊 Found ratings from {agencies_count} agencies")
                    
                    # Log the agencies found for verification (with safe handling)
                    try:
                        agencies = [agency.get("agency", "Unknown") for agency in credit_rating_result["credit_rating"] if agency]
                        logger.info(f"🏛️ Rating agencies found: {', '.join(agencies)}")
                    except Exception as agency_error:
                        logger.warning(f"⚠️ Error processing agencies list: {agency_error}")
                        logger.info(f"🔍 Credit rating structure: {type(credit_rating_result['credit_rating'])}")
                
                # Log geographic context
                geographic_region = result.get("geographic_region", "Unknown")
                logger.info(f"🌍 Geographic region detected: {geographic_region}")
                
                return credit_rating_result
            else:
                logger.warning(f"⚠️ Credit rating research failed for {plant_name}")
                if credit_rating_result and credit_rating_result.get("error"):
                    logger.warning(f"   Error: {credit_rating_result['error']}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Credit rating research failed for {plant_name}: {str(e)}")
            return None
    
    def merge_credit_rating_data(self, existing_data: Dict, new_rating_data: Dict) -> Dict:
        """
        Merge new credit rating data with existing financial data
        
        Args:
            existing_data: Existing financial data
            new_rating_data: New credit rating data from research (contains all fields from agent)
            
        Returns:
            Updated financial data with merged credit rating
        """
        try:
            # Create a copy to avoid modifying original
            updated_data = existing_data.copy()
            
            # Extract all credit rating fields from agent response
            if "credit_rating" in new_rating_data:
                new_credit_rating = new_rating_data["credit_rating"]
                
                # If existing data has no credit_rating or it's empty/incomplete, replace it
                if ("credit_rating" not in updated_data or 
                    not updated_data["credit_rating"] or 
                    self.check_credit_rating_completeness(updated_data)["needs_research"]):
                    
                    # Use all data from agent response
                    updated_data["credit_rating"] = new_credit_rating
                    updated_data["credit_rating_note"] = new_rating_data.get("credit_rating_note", "")
                    updated_data["currency"] = new_rating_data.get("currency", "")
                    updated_data["level"] = new_rating_data.get("level", "")
                    
                    logger.info(f"✅ Added new credit rating data ({len(new_credit_rating)} agencies)")
                    logger.info(f"✅ Added credit_rating_note: {updated_data['credit_rating_note'][:100]}...")
                    logger.info(f"✅ Added currency: {updated_data['currency']}")
                    logger.info(f"✅ Added level: {updated_data['level']}")
                    
                else:
                    # Merge with existing data (avoid duplicates)
                    existing_credit_rating = updated_data["credit_rating"]
                    
                    for new_agency in new_credit_rating:
                        # Check if agency already exists
                        agency_exists = False
                        for existing_agency in existing_credit_rating:
                            if (existing_agency.get("agency") == new_agency.get("agency") and 
                                existing_agency.get("name") == new_agency.get("name")):
                                agency_exists = True
                                break
                        
                        if not agency_exists:
                            existing_credit_rating.append(new_agency)
                            logger.info(f"✅ Added new agency: {new_agency.get('agency', 'Unknown')}")
                    
                    updated_data["credit_rating"] = existing_credit_rating
                    
                    # Update other fields from agent response (always use latest from agent)
                    updated_data["credit_rating_note"] = new_rating_data.get("credit_rating_note", "")
                    updated_data["currency"] = new_rating_data.get("currency", "")
                    updated_data["level"] = new_rating_data.get("level", "")
                    
                    logger.info(f"✅ Updated credit_rating_note from agent")
                    logger.info(f"✅ Updated currency: {updated_data['currency']}")
                    logger.info(f"✅ Updated level: {updated_data['level']}")
                
                logger.info("✅ Credit rating data merged successfully")
            else:
                logger.warning("⚠️ No credit_rating field in research result")
            
            return updated_data
            
        except Exception as e:
            logger.error(f"❌ Failed to merge credit rating data: {str(e)}")
            return existing_data
    

    

    
    async def process_final_output_directory(self, final_output_dir: Path, country_name: str = "Unknown", plant_name_override: str = None) -> bool:
        """
        Process final_output directory to check and enhance credit ratings
        
        Args:
            final_output_dir: Path to the final_output directory
            country_name: Country where the plant is located
            plant_name_override: Override plant name (use instead of extracting from data)
            
        Returns:
            True if processing was successful, False otherwise
        """
        try:
            logger.info(f"🔍 Processing final_output directory: {final_output_dir}")
            
            if not final_output_dir.exists():
                logger.error(f"❌ Final output directory not found: {final_output_dir}")
                return False
            
            # Find financial details file
            financial_files = list(final_output_dir.glob("*financial_details.json"))
            
            if not financial_files:
                logger.warning(f"⚠️ No financial_details.json file found in {final_output_dir}")
                return False
            
            financial_file = financial_files[0]
            logger.info(f"📄 Found financial file: {financial_file.name}")
            
            # Load financial data
            with open(financial_file, 'r', encoding='utf-8') as f:
                financial_data = json.load(f)
            
            # Use override plant name if provided, otherwise extract from data
            if plant_name_override:
                plant_name = plant_name_override
                logger.info(f"🏭 Using provided plant name: {plant_name}")
            else:
                plant_name = self.extract_plant_name_from_data(financial_data)
                logger.info(f"🏭 Extracted plant name from data: {plant_name}")
            
            # Check credit rating completeness
            completeness = self.check_credit_rating_completeness(financial_data)
            logger.info(f"📊 Credit rating analysis: {completeness['reason']}")
            
            if not completeness["needs_research"]:
                logger.info("✅ Credit rating data is complete - no research needed")
                return True
            
            if not self.agent_available:
                logger.warning("⚠️ Credit rating research needed but agent not available")
                return False
            
            # Research credit rating
            logger.info(f"🔍 Researching credit rating for {plant_name} in {country_name}...")
            new_rating_data = await self.research_credit_rating(plant_name, country_name)
            
            if not new_rating_data:
                logger.warning(f"⚠️ Credit rating research failed for {plant_name}")
                return False
            
            # Merge new data with existing
            updated_data = self.merge_credit_rating_data(financial_data, new_rating_data)
            
            # Save updated file
            backup_file = financial_file.with_suffix('.json.backup')
            financial_file.rename(backup_file)
            logger.info(f"💾 Created backup: {backup_file.name}")
            
            with open(financial_file, 'w', encoding='utf-8') as f:
                json.dump(updated_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ Updated financial file with credit rating data: {financial_file.name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to process final_output directory: {str(e)}")
            return False
    
    async def process_plant_credit_rating(self, plant_name: str, country_name: str) -> bool:
        """
        Process credit rating for a specific plant in final_output directory
        
        Args:
            plant_name: Name of the plant to process
            country_name: Country where the plant is located
            
        Returns:
            True if processing was successful, False otherwise
        """
        try:
            # Convert plant name to directory name format
            safe_plant_name = plant_name.replace(" ", "_").replace('"', '').replace("'", "")
            
            # Look for final_output directory
            final_output_base = Path("final_output")
            if not final_output_base.exists():
                logger.error("❌ final_output directory not found")
                return False
            
            # Find plant directory
            plant_dirs = [
                final_output_base / safe_plant_name,
                final_output_base / plant_name.replace(" ", "_"),
                final_output_base / plant_name
            ]
            
            plant_dir = None
            for dir_path in plant_dirs:
                if dir_path.exists():
                    plant_dir = dir_path
                    break
            
            if not plant_dir:
                logger.error(f"❌ Plant directory not found for: {plant_name}")
                logger.info(f"   Searched in: {[str(d) for d in plant_dirs]}")
                return False
            
            logger.info(f"📁 Found plant directory: {plant_dir}")
            
            # Process the directory with the actual plant name
            return await self.process_final_output_directory(plant_dir, country_name, plant_name)
            
        except Exception as e:
            logger.error(f"❌ Failed to process plant credit rating: {str(e)}")
            return False

# Convenience function for integration with the main pipeline
async def enhance_credit_rating_before_s3_upload(plant_name: str, country_name: str) -> bool:
    """
    Enhance credit rating data before S3 upload
    
    Args:
        plant_name: Name of the plant to process
        country_name: Country where the plant is located
        
    Returns:
        True if enhancement was successful or not needed, False if failed
    """
    try:
        integrator = CreditRatingIntegrator()
        result = await integrator.process_plant_credit_rating(plant_name, country_name)
        return result
    except Exception as e:
        logger.error(f"❌ Credit rating enhancement failed: {str(e)}")
        return False

# Function to check if credit rating enhancement is needed (quick check)
def check_if_credit_rating_needed(final_output_dir: Path) -> bool:
    """
    Quick check if credit rating enhancement is needed
    
    Args:
        final_output_dir: Path to final_output directory
        
    Returns:
        True if enhancement is needed, False otherwise
    """
    try:
        integrator = CreditRatingIntegrator()
        
        # Find financial details file
        financial_files = list(final_output_dir.glob("*financial_details.json"))
        if not financial_files:
            return False
        
        # Load and check
        with open(financial_files[0], 'r', encoding='utf-8') as f:
            financial_data = json.load(f)
        
        completeness = integrator.check_credit_rating_completeness(financial_data)
        return completeness["needs_research"]
        
    except Exception:
        return False

if __name__ == "__main__":
    import asyncio
    import argparse
    
    parser = argparse.ArgumentParser(description="Credit Rating Integration Tool")
    parser.add_argument("plant_name", help="Name of the plant to process")
    parser.add_argument("country_name", help="Country where the plant is located")
    parser.add_argument("--check-only", action="store_true", help="Only check if enhancement is needed")
    
    args = parser.parse_args()
    
    if args.check_only:
        # Check mode
        safe_plant_name = args.plant_name.replace(" ", "_")
        final_output_dir = Path("final_output") / safe_plant_name
        
        if check_if_credit_rating_needed(final_output_dir):
            print(f"✅ Credit rating enhancement needed for {args.plant_name}")
        else:
            print(f"❌ Credit rating enhancement not needed for {args.plant_name}")
    else:
        # Process mode
        async def main():
            success = await enhance_credit_rating_before_s3_upload(args.plant_name, args.country_name)
            if success:
                print(f"✅ Credit rating enhancement completed for {args.plant_name} in {args.country_name}")
            else:
                print(f"❌ Credit rating enhancement failed for {args.plant_name} in {args.country_name}")
        
        asyncio.run(main())