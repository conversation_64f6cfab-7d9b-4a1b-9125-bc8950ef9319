from __future__ import annotations

from dataclasses import dataclass, field
from typing import TypedDict, Optional, Literal

from langgraph.graph import add_messages
from typing_extensions import Annotated


import operator


class OverallState(TypedDict):
    messages: Annotated[list, add_messages]
    search_query: Annotated[list, operator.add]
    web_research_result: Annotated[list, operator.add]
    sources_gathered: Annotated[list, operator.add]
    initial_search_query_count: int
    max_research_loops: int
    research_loop_count: int
    reasoning_model: str
    # Credit rating specific fields
    search_mode: Optional[Literal["general", "credit_rating"]]
    power_plant_name: Optional[str]
    geographic_region: Optional[str]
    credit_rating_strategy: Optional[Literal["primary", "fallback"]]
    credit_rating_result: Optional[dict]


class ReflectionState(TypedDict):
    is_sufficient: bool
    knowledge_gap: str
    follow_up_queries: Annotated[list, operator.add]
    research_loop_count: int
    number_of_ran_queries: int


class Query(TypedDict):
    query: str
    rationale: str


class QueryGenerationState(TypedDict):
    search_query: list[Query]


class WebSearchState(TypedDict):
    search_query: str
    id: str


class CreditRatingQueryGenerationState(TypedDict):
    """State for credit rating specific query generation."""
    primary_queries: list[str]
    fallback_queries: list[str]
    rationale: str
    power_plant_name: str
    geographic_region: Optional[str]


class CreditRatingReflectionState(TypedDict):
    """State for credit rating specific reflection."""
    is_sufficient: bool
    knowledge_gap: str
    follow_up_queries: Annotated[list, operator.add]
    research_loop_count: int
    number_of_ran_queries: int
    rating_completeness: str
    suggested_strategy: Optional[str]


@dataclass
class SearchStateOutput:
    running_summary: str = field(default=None)  # Final report
