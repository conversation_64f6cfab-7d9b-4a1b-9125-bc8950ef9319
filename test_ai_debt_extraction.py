#!/usr/bin/env python3
"""
Test script for AI-enhanced debt extraction in financial assumptions
Tests the zero-debt handling logic and notes-based extraction
"""

import asyncio
import json
import logging
from calculator import FinancialMetricsCalculator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_zero_debt_cost_calculation():
    """Test that cost of debt is 0.0 when total debt is 0"""
    logger.info("🧪 Testing zero-debt cost calculation...")
    
    calculator = FinancialMetricsCalculator()
    
    # Test case 1: Zero debt scenario (use larger, more realistic values)
    balance_sheet_data = {
        "balance_sheet": {
            "Property, plant and equipment": 50000000,  # 50M - realistic for power company
            "total_equity": 30000000,  # 30M - realistic equity
            "short_term_borrowings": 0,  # Zero debt
            "Long_Term_borrowing": 0,    # Zero debt
            "interest_bearing_loans_borrowings": 0,  # Zero debt
            "current_assets": 10000000,  # 10M
            "current_liabilities": 5000000,  # 5M
            "cash_and_cash_equivalents": 2000000,  # 2M
            "country": "India",
            "currency": "INR"
        }
    }
    
    profit_loss_data = {
        "profit_loss": {
            "revenue": 80000000,  # 80M revenue - realistic for power company
            "finance_cost": 0,  # Zero finance cost
            "profit_before_tax": 10000000,  # 10M profit
            "net_income": 7500000  # 7.5M net income
        }
    }
    
    # Calculate metrics
    result = calculator.calculate_financial_metrics(balance_sheet_data, profit_loss_data)
    
    # Verify cost of debt is 0.0
    cost_of_debt = result["wacc_breakup"]["cost_of_debt"]
    assert cost_of_debt == 0.0, f"Expected cost_of_debt=0.0 for zero debt, got {cost_of_debt}"
    
    logger.info(f"✅ Test 1 PASSED: Cost of debt = {cost_of_debt:.4f} for zero debt scenario")
    
    # Test case 2: Non-zero debt scenario
    balance_sheet_data["balance_sheet"]["Long_Term_borrowing"] = 20000000  # Add 20M debt
    profit_loss_data["profit_loss"]["finance_cost"] = 1600000  # Add 1.6M finance cost
    
    result2 = calculator.calculate_financial_metrics(balance_sheet_data, profit_loss_data)
    cost_of_debt2 = result2["wacc_breakup"]["cost_of_debt"]
    
    # Should be 1600000/20000000 = 0.08 = 8%
    expected_cost = 1600000 / 20000000
    assert abs(cost_of_debt2 - expected_cost) < 0.001, f"Expected cost_of_debt={expected_cost:.4f}, got {cost_of_debt2:.4f}"
    
    logger.info(f"✅ Test 2 PASSED: Cost of debt = {cost_of_debt2:.4f} for debt scenario (expected {expected_cost:.4f})")
    
    return True

def test_debt_extraction_structure():
    """Test the structure of the new AI debt extraction methods"""
    logger.info("🧪 Testing AI debt extraction method structure...")
    
    calculator = FinancialMetricsCalculator()
    
    # Check if new methods exist
    assert hasattr(calculator, 'get_debt_notes_adaptive'), "get_debt_notes_adaptive method missing"
    assert hasattr(calculator, 'extract_debt_data_for_assumptions'), "extract_debt_data_for_assumptions method missing"
    assert hasattr(calculator, 'get_assumption_debt_prompt'), "get_assumption_debt_prompt method missing"
    assert hasattr(calculator, '_extract_basic_values_with_ai_debt'), "_extract_basic_values_with_ai_debt method missing"
    assert hasattr(calculator, 'calculate_financial_metrics_with_ai_debt_extraction'), "calculate_financial_metrics_with_ai_debt_extraction method missing"
    
    logger.info("✅ All required AI debt extraction methods are present")
    
    # Test prompt generation
    prompt = calculator.get_assumption_debt_prompt("Statement of Balance Sheet", "INR", ["13", "14", "15"])
    assert "NOTES SECTION SOURCE" in prompt, "Prompt should mention notes section"
    assert "borrowing items only" in prompt, "Prompt should specify borrowing items only"
    # Note: Debt-free handling is done in calculation logic, not in the prompt
    
    logger.info("✅ AI prompt generation working correctly")
    
    return True

async def test_ai_debt_extraction_mock():
    """Test AI debt extraction with mock data (without actual OpenAI calls)"""
    logger.info("🧪 Testing AI debt extraction logic (mock)...")
    
    calculator = FinancialMetricsCalculator()
    
    # Mock balance sheet and P&L data
    bs = {
        "Property, plant and equipment": 1000000,
        "total_equity": 500000,
        "short_term_borrowings": 50000,  # Fallback values
        "Long_Term_borrowing": 100000,   # Fallback values
        "current_assets": 200000,
        "current_liabilities": 100000,
        "cash_and_cash_equivalents": 50000,
        "country": "India",
        "currency": "INR"
    }
    
    pl = {
        "revenue": 800000,
        "finance_cost": 12000,
        "profit_before_tax": 100000,
        "net_income": 75000
    }
    
    # Test without AI (should use fallback)
    result = await calculator._extract_basic_values_with_ai_debt(
        bs, pl, None, None, None  # No images, should use fallback
    )
    
    # Should use balance sheet values as fallback
    assert result["short_term_debt"] == 50000, f"Expected short_term_debt=50000, got {result['short_term_debt']}"
    assert result["long_term_debt"] == 100000, f"Expected long_term_debt=100000, got {result['long_term_debt']}"
    assert result["total_debt"] == 150000, f"Expected total_debt=150000, got {result['total_debt']}"
    
    logger.info("✅ AI debt extraction fallback logic working correctly")
    
    return True

def main():
    """Run all tests"""
    logger.info("🚀 Starting AI-enhanced debt extraction tests...")
    
    try:
        # Test 1: Zero debt cost calculation
        test_zero_debt_cost_calculation()
        
        # Test 2: Method structure
        test_debt_extraction_structure()
        
        # Test 3: AI extraction logic (mock)
        asyncio.run(test_ai_debt_extraction_mock())
        
        logger.info("🎉 ALL TESTS PASSED! AI-enhanced debt extraction is working correctly.")
        logger.info("✅ Key features verified:")
        logger.info("   - Zero debt → cost_of_debt = 0.0 (no artificial minimum)")
        logger.info("   - Non-zero debt → cost_of_debt = finance_cost / total_debt")
        logger.info("   - AI extraction methods are properly structured")
        logger.info("   - Fallback to balance sheet values when AI unavailable")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ TEST FAILED: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
