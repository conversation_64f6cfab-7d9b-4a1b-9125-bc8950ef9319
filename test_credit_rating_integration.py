#!/usr/bin/env python3
"""
Test Credit Rating Integration
Date: 2025-01-11
User: Gangatharan G
Purpose: Test the credit rating integration with sample data
"""

import json
import asyncio
import tempfile
import shutil
from pathlib import Path
from credit_rating_integration import CreditRatingIntegrator, enhance_credit_rating_before_s3_upload

def create_test_financial_data(scenario: str) -> dict:
    """Create test financial data for different scenarios"""
    
    base_data = {
        "pk": "test-entity-123",
        "sk": "financials",
        "plant_name": "Seabank power station",
        "currency": "GBP",
        "amount_in": "millions"
    }
    
    if scenario == "missing_field":
        # No credit_rating field at all
        return base_data
    
    elif scenario == "empty_array":
        # Empty credit_rating array
        return {**base_data, "credit_rating": []}
    
    elif scenario == "empty_values":
        # credit_rating with empty values
        return {
            **base_data,
            "credit_rating": [
                {
                    "agency": "",
                    "name": "",
                    "yearwise_rating": [
                        {
                            "rating": "",
                            "rating_trunc": "",
                            "year": ""
                        }
                    ]
                }
            ]
        }
    
    elif scenario == "partial_empty":
        # Some agencies with data, some empty
        return {
            **base_data,
            "credit_rating": [
                {
                    "agency": "CRISIL",
                    "name": "CRISIL Limited",
                    "yearwise_rating": [
                        {
                            "rating": "BBB+ Stable",
                            "rating_trunc": "BBB+",
                            "year": "2023"
                        }
                    ]
                },
                {
                    "agency": "",
                    "name": "",
                    "yearwise_rating": [
                        {
                            "rating": "",
                            "rating_trunc": "",
                            "year": ""
                        }
                    ]
                }
            ]
        }
    
    elif scenario == "complete":
        # Complete credit rating data
        return {
            **base_data,
            "credit_rating": [
                {
                    "agency": "CRISIL",
                    "name": "CRISIL Limited",
                    "yearwise_rating": [
                        {
                            "rating": "BBB+ Stable",
                            "rating_trunc": "BBB+",
                            "year": "2023"
                        },
                        {
                            "rating": "BBB Stable",
                            "rating_trunc": "BBB",
                            "year": "2022"
                        }
                    ]
                },
                {
                    "agency": "SP",
                    "name": "S&P Global Ratings",
                    "yearwise_rating": [
                        {
                            "rating": "BBB- Positive",
                            "rating_trunc": "BBB-",
                            "year": "2023"
                        }
                    ]
                }
            ]
        }
    
    return base_data

async def test_scenario(scenario: str, integrator: CreditRatingIntegrator):
    """Test a specific scenario"""
    print(f"\n{'='*60}")
    print(f"Testing Scenario: {scenario.upper()}")
    print(f"{'='*60}")
    
    # Create test data
    test_data = create_test_financial_data(scenario)
    print(f"📄 Test data created for scenario: {scenario}")
    
    # Check completeness
    completeness = integrator.check_credit_rating_completeness(test_data)
    print(f"📊 Completeness Analysis:")
    print(f"   - Has credit_rating field: {completeness['has_credit_rating_field']}")
    print(f"   - Is empty: {completeness['is_empty']}")
    print(f"   - Is incomplete: {completeness['is_incomplete']}")
    print(f"   - Needs research: {completeness['needs_research']}")
    print(f"   - Reason: {completeness['reason']}")
    
    if completeness['missing_agencies']:
        print(f"   - Missing agencies: {completeness['missing_agencies']}")
    
    # Test plant name extraction
    plant_name = integrator.extract_plant_name_from_data(test_data)
    print(f"🏭 Extracted plant name: {plant_name}")
    
    # Test with sample country for research (if needed)
    if completeness['needs_research'] and integrator.agent_available:
        print(f"🔍 Would research: 'Credit rating of {plant_name} United Kingdom'")
    
    return completeness['needs_research']

async def test_full_integration():
    """Test the full integration with a temporary directory"""
    print(f"\n{'='*60}")
    print(f"Testing Full Integration")
    print(f"{'='*60}")
    
    # Create temporary directory structure
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create final_output structure
        final_output_dir = temp_path / "final_output"
        plant_dir = final_output_dir / "Seabank_power_station"
        plant_dir.mkdir(parents=True)
        
        # Create test financial file with missing credit rating
        test_data = create_test_financial_data("missing_field")
        financial_file = plant_dir / "Seabank_power_station_financial_details.json"
        
        with open(financial_file, 'w') as f:
            json.dump(test_data, f, indent=2)
        
        print(f"📁 Created test directory: {plant_dir}")
        print(f"📄 Created test file: {financial_file.name}")
        
        # Change to temp directory to simulate real environment
        original_cwd = Path.cwd()
        try:
            import os
            os.chdir(temp_path)
            
            # Test the integration
            integrator = CreditRatingIntegrator()
            result = await integrator.process_final_output_directory(plant_dir, "United Kingdom")
            
            print(f"🔍 Integration result: {'✅ Success' if result else '❌ Failed'}")
            
            # Check if file was modified (backup should exist)
            backup_file = financial_file.with_suffix('.json.backup')
            if backup_file.exists():
                print(f"💾 Backup file created: {backup_file.name}")
                
                # Load updated file
                with open(financial_file, 'r') as f:
                    updated_data = json.load(f)
                
                if "_credit_rating_research" in updated_data:
                    print(f"✅ Research metadata added to file")
                
                if "credit_rating" in updated_data and updated_data["credit_rating"]:
                    print(f"✅ Credit rating data added to file")
                    print(f"   - Number of agencies: {len(updated_data['credit_rating'])}")
                else:
                    print(f"⚠️ No credit rating data added (research may have failed)")
            else:
                print(f"ℹ️ No backup file created (no changes made)")
        
        finally:
            os.chdir(original_cwd)

async def main():
    """Main test function"""
    print("🧪 Credit Rating Integration Test Suite")
    print("=" * 60)
    
    # Initialize integrator
    integrator = CreditRatingIntegrator()
    
    # Test different scenarios
    scenarios = [
        "missing_field",
        "empty_array", 
        "empty_values",
        "partial_empty",
        "complete"
    ]
    
    needs_research_count = 0
    
    for scenario in scenarios:
        needs_research = await test_scenario(scenario, integrator)
        if needs_research:
            needs_research_count += 1
    
    print(f"\n{'='*60}")
    print(f"Summary: {needs_research_count}/{len(scenarios)} scenarios need research")
    print(f"{'='*60}")
    
    # Test full integration if agent is available
    if integrator.agent_available:
        await test_full_integration()
    else:
        print(f"\n⚠️ Skipping full integration test - credit rating agent not available")
    
    print(f"\n✅ Test suite completed!")

if __name__ == "__main__":
    asyncio.run(main())