# Credit Rating Integration

**Date:** 2025-01-11  
**User:** Gangatharan G  
**Purpose:** Integrate credit rating research agent with the existing financial pipeline

## Overview

This integration automatically detects missing or incomplete credit rating data in the financial pipeline and uses an AI research agent to find and populate credit rating information before uploading to S3.

## Integration Points

### 1. Main Pipeline Integration
- **File:** `financial_full_pipeline_api.py`
- **Location:** Between pipeline completion and S3 upload
- **Trigger:** After `final_output` directory is created, before S3 upload

### 2. Credit Rating Detection
The system triggers credit rating research in these scenarios:

#### Scenario 1: Missing Parameter
```json
{
  "pk": "entity-123",
  "plant_name": "Seabank power station"
  // No "credit_rating" field at all
}
```

#### Scenario 2: Empty Array
```json
{
  "credit_rating": []
}
```

#### Scenario 3: Empty Values
```json
{
  "credit_rating": [
    {
      "agency": "",
      "name": "",
      "yearwise_rating": [
        {
          "rating": "",
          "rating_trunc": "",
          "year": ""
        }
      ]
    }
  ]
}
```

## Files Added/Modified

### New Files
1. **`credit_rating_integration.py`** - Main integration module
2. **`test_credit_rating_integration.py`** - Test suite
3. **`CREDIT_RATING_INTEGRATION.md`** - This documentation

### Modified Files
1. **`financial_full_pipeline_api.py`** - Added credit rating enhancement step

## How It Works

### 1. Pipeline Flow
```
Pipeline Processing → final_output created → Credit Rating Enhancement → S3 Upload
```

### 2. Enhancement Process
1. **Detection:** Check if credit rating data is missing/incomplete
2. **Query Construction:** Create geographic-aware query: `"Credit rating of {plant_name} {country_name}"`
3. **Research:** Use AI agent with proper geographic targeting
4. **Merge:** Update financial_details.json with research results
5. **Backup:** Create backup of original file
6. **Continue:** Proceed with S3 upload

### 3. Geographic Intelligence
The system automatically targets appropriate rating agencies based on country:
- **UK**: Fitch, S&P, Moody's
- **India**: CRISIL, ICRA, CARE Ratings, India Ratings
- **US**: Moody's, S&P, Fitch
- **Europe**: Fitch, S&P, Moody's, Scope Ratings

### 4. Error Handling
- If credit rating research fails, the pipeline continues with S3 upload
- Original files are backed up before modification
- Comprehensive logging for debugging

## Usage

### Automatic Integration
The integration runs automatically in the main pipeline:

```python
# In process_clem_pipeline function
credit_rating_success = await enhance_credit_rating_before_s3_upload(plant_name, country_name)
```

### Manual Testing
```bash
# Test specific plant
python credit_rating_integration.py "Seabank power station" "United Kingdom"

# Check if enhancement is needed
python credit_rating_integration.py "Seabank power station" "United Kingdom" --check-only

# Run test suite
python test_credit_rating_integration.py
```

## Configuration

### Environment Variables
The credit rating agent requires:
- `GEMINI_API_KEY` - For Google Gemini API access
- Other agent-specific environment variables

### Dependencies
- Credit rating agent modules in `src/agent/`
- LangChain and related packages
- Google Generative AI packages

## API Response Enhancement

The API response now includes credit rating enhancement status:

```json
{
  "processing_summary": {
    "pipeline_mode": "clem_manual",
    "processing_time": "2025-01-11 14:35:16",
    "credit_rating_enhancement": {
      "available": true,
      "attempted": true,
      "status": "completed"
    }
  }
}
```

## Query Format

The integration uses the exact query format expected by the credit rating agent:

### Input Format
```
"Credit rating of {plant_name} {country_name}"
```

### Examples
- **UK Plant**: `"Credit rating of Seabank power station United Kingdom"`
- **Indian Plant**: `"Credit rating of Ib Valley Thermal Power Station India"`
- **US Plant**: `"Credit rating of Ivanpah Solar Power Facility United States"`

This format ensures:
- ✅ Proper geographic detection by the agent
- ✅ Region-appropriate rating agency targeting
- ✅ Consistent with the existing agent system

## Logging

Enhanced logging provides visibility into the credit rating process:

```
🔍 Starting credit rating enhancement for Seabank power station in United Kingdom
📊 Credit rating analysis: credit_rating field is missing entirely
🤖 Executing credit rating research agent with query: 'Credit rating of Seabank power station United Kingdom'
✅ Credit rating research successful for Seabank power station
📊 Found ratings from 2 agencies
🏛️ Rating agencies found: Fitch, S&P
🌍 Geographic region detected: United Kingdom
✅ Credit rating enhancement completed for Seabank power station
```

## Example Output

### Before Enhancement
```json
{
  "pk": "96013f52-793e-53a3-a177-92c000fda5e3",
  "plant_name": "Seabank power station"
  // No credit_rating field
}
```

### After Enhancement
```json
{
  "pk": "96013f52-793e-53a3-a177-92c000fda5e3",
  "plant_name": "Seabank power station",
  "credit_rating": [
    {
      "agency": "SP",
      "name": "S&P Global Ratings",
      "yearwise_rating": [
        {
          "rating": "BBB+ Stable",
          "rating_trunc": "BBB+",
          "year": "2023"
        }
      ]
    }
  ],
  "_credit_rating_research": {
    "researched_at": "2025-01-11T14:35:16.123456",
    "research_method": "automated_agent",
    "data_source": "web_research",
    "level": "power_plant"
  }
}
```

## Troubleshooting

### Common Issues

1. **Agent Not Available**
   ```
   ⚠️ Credit rating integration not available: No module named 'agent'
   ```
   - Ensure `src/agent/` directory exists with required modules
   - Check GEMINI_API_KEY environment variable

2. **Research Fails**
   ```
   ⚠️ Credit rating research failed for Plant Name
   ```
   - Check internet connectivity
   - Verify API key validity
   - Review agent logs for specific errors

3. **File Not Found**
   ```
   ❌ Plant directory not found for: Plant Name
   ```
   - Ensure final_output directory structure is correct
   - Check plant name formatting

### Debug Mode
Enable detailed logging by setting log level to DEBUG:

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## Future Enhancements

1. **Caching:** Cache research results to avoid repeated API calls
2. **Validation:** Enhanced validation of research results
3. **Fallback Sources:** Multiple data sources for credit ratings
4. **Scheduling:** Periodic updates of existing credit rating data

## Support

For issues or questions about the credit rating integration:
1. Check the logs for detailed error messages
2. Run the test suite to verify functionality
3. Review the agent configuration and dependencies