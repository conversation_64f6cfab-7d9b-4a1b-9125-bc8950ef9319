"""
Financial Metrics Calculator - Processes Balance Sheet & Profit Loss Data
Author: Gangatharangurusamy
Date: 2025-07-21
Description: Calculate all financial metrics from extracted Balance Sheet and P&L data
Input: Two separate JSON outputs from focused extraction
Output: Complete financial metrics for JSON generation
Enhanced: Added more countries and currency exchange API
"""

import logging
import json
import requests
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime
import math
import os
import base64

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Warning: python-dotenv not installed")

# Required imports for AI-based debt extraction
try:
    import openai
    from openai import OpenAI
except ImportError as e:
    print(f"Warning: OpenAI not available: {e}")
    openai = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FinancialMetricsCalculator:
    """
    Calculate comprehensive financial metrics from Balance Sheet and Profit & Loss data
    Enhanced with more countries and currency exchange API
    """
    
    # Enhanced Country-based depreciation rates (added more countries)
    DEPRECIATION_RATES = {
        # Existing countries
        "India": 0.15,          # 15% for India
        "South Africa": 0.50,   # 50% for South Africa  
        "United States": 0.35,  # 35% for United States
        "Germany": 0.30,        # 30% for Germany
        
        # Added countries
        "United Kingdom": 0.25,  # 25% for UK
        "Canada": 0.30,         # 30% for Canada
        "Australia": 0.375,     # 37.5% for Australia
        "Japan": 0.20,          # 20% for Japan
        "China": 0.25,          # 25% for China
        "France": 0.31,         # 31% for France
        "Italy": 0.24,          # 24% for Italy
        "Spain": 0.25,          # 25% for Spain
        "Netherlands": 0.25,    # 25% for Netherlands
        "Sweden": 0.20,         # 20% for Sweden
        "Norway": 0.22,         # 22% for Norway
        "Denmark": 0.22,        # 22% for Denmark
        "Finland": 0.20,        # 20% for Finland
        "Switzerland": 0.20,    # 20% for Switzerland
        "Austria": 0.25,        # 25% for Austria
        "Belgium": 0.29,        # 29% for Belgium
        "Brazil": 0.34,         # 34% for Brazil
        "Mexico": 0.30,         # 30% for Mexico
        "Argentina": 0.35,      # 35% for Argentina
        "Chile": 0.27,          # 27% for Chile
        "Russia": 0.20,         # 20% for Russia
        "South Korea": 0.25,    # 25% for South Korea
        "Singapore": 0.17,      # 17% for Singapore
        "Malaysia": 0.24,       # 24% for Malaysia
        "Thailand": 0.20,       # 20% for Thailand
        "Indonesia": 0.22,      # 22% for Indonesia
        "Philippines": 0.30,    # 30% for Philippines
        "Vietnam": 0.20,        # 20% for Vietnam
        "UAE": 0.09,            # 9% for UAE
        "Saudi Arabia": 0.20,   # 20% for Saudi Arabia
        "Egypt": 0.225,         # 22.5% for Egypt
        "Nigeria": 0.30,        # 30% for Nigeria
        "Kenya": 0.30,          # 30% for Kenya
        "Ghana": 0.25,          # 25% for Ghana
        "New Zealand": 0.28,    # 28% for New Zealand
        "Ireland": 0.125,       # 12.5% for Ireland
        "Luxembourg": 0.24,     # 24% for Luxembourg
        "Portugal": 0.21,       # 21% for Portugal
        "Israel": 0.23,         # 23% for Israel
        "Turkey": 0.20,         # 20% for Turkey
        "Poland": 0.19,         # 19% for Poland
        "Czech Republic": 0.19, # 19% for Czech Republic
        "Hungary": 0.09,        # 9% for Hungary
        "Slovakia": 0.21,       # 21% for Slovakia
        "Slovenia": 0.19,       # 19% for Slovenia
        "Croatia": 0.18,        # 18% for Croatia
        "Romania": 0.16,        # 16% for Romania
        "Bulgaria": 0.10,       # 10% for Bulgaria
        "Estonia": 0.20,        # 20% for Estonia
        "Latvia": 0.20,         # 20% for Latvia
        "Lithuania": 0.15,      # 15% for Lithuania
        
        "Unknown": 0.35         # Default fallback
    }
    
    # Enhanced Country-based tax rates (added more countries)
    TAX_RATES = {
        # Existing countries
        "India": 0.2515,         # 26.4% for India
        "South Africa": 0.28,   # 28% for South Africa
        "United States": 0.21,  # 21% for United States
        "Germany": 0.32,        # 32% for Germany
        
        # Added countries
        "United Kingdom": 0.25,  # 25% for UK
        "Canada": 0.27,         # 27% for Canada
        "Australia": 0.30,      # 30% for Australia
        "Japan": 0.31,          # 31% for Japan
        "China": 0.25,          # 25% for China
        "France": 0.32,         # 32% for France
        "Italy": 0.27,          # 27% for Italy
        "Spain": 0.25,          # 25% for Spain
        "Netherlands": 0.25,    # 25% for Netherlands
        "Sweden": 0.20,         # 20% for Sweden
        "Norway": 0.22,         # 22% for Norway
        "Denmark": 0.22,        # 22% for Denmark
        "Finland": 0.20,        # 20% for Finland
        "Switzerland": 0.19,    # 19% for Switzerland
        "Austria": 0.25,        # 25% for Austria
        "Belgium": 0.29,        # 29% for Belgium
        "Brazil": 0.34,         # 34% for Brazil
        "Mexico": 0.30,         # 30% for Mexico
        "Argentina": 0.35,      # 35% for Argentina
        "Chile": 0.27,          # 27% for Chile
        "Russia": 0.20,         # 20% for Russia
        "South Korea": 0.25,    # 25% for South Korea
        "Singapore": 0.17,      # 17% for Singapore
        "Malaysia": 0.24,       # 24% for Malaysia
        "Thailand": 0.20,       # 20% for Thailand
        "Indonesia": 0.22,      # 22% for Indonesia
        "Philippines": 0.30,    # 30% for Philippines
        "Vietnam": 0.20,        # 20% for Vietnam
        "UAE": 0.09,            # 9% for UAE
        "Saudi Arabia": 0.20,   # 20% for Saudi Arabia
        "Egypt": 0.225,         # 22.5% for Egypt
        "Nigeria": 0.30,        # 30% for Nigeria
        "Kenya": 0.30,          # 30% for Kenya
        "Ghana": 0.25,          # 25% for Ghana
        "New Zealand": 0.28,    # 28% for New Zealand
        "Ireland": 0.125,       # 12.5% for Ireland
        "Luxembourg": 0.24,     # 24% for Luxembourg
        "Portugal": 0.21,       # 21% for Portugal
        "Israel": 0.23,         # 23% for Israel
        "Turkey": 0.20,         # 20% for Turkey
        "Poland": 0.19,         # 19% for Poland
        "Czech Republic": 0.19, # 19% for Czech Republic
        "Hungary": 0.09,        # 9% for Hungary
        "Slovakia": 0.21,       # 21% for Slovakia
        "Slovenia": 0.19,       # 19% for Slovenia
        "Croatia": 0.18,        # 18% for Croatia
        "Romania": 0.16,        # 16% for Romania
        "Bulgaria": 0.10,       # 10% for Bulgaria
        "Estonia": 0.20,        # 20% for Estonia
        "Latvia": 0.20,         # 20% for Latvia
        "Lithuania": 0.15,      # 15% for Lithuania
        
        "Unknown": 0.25         # 25% default
    }
    
    # Enhanced Country-based transition credits (added more currencies)
    TRANSITION_CREDITS = {
        # Existing currencies
        "INR": 833.9,      # India - Renewable energy transition
        "ZAR": 180.0,      # South Africa - Coal to renewable transition
        "USD": 10.0,       # United States - Clean energy transition
        "EUR": 8.5,        # European Union - Green Deal transition
        
        # Added currencies
        "GBP": 7.50,       # United Kingdom - Green finance initiative
        "CAD": 13.5,       # Canada - Clean technology transition
        "AUD": 15.0,       # Australia - Renewable energy scheme
        "JPY": 1100.0,     # Japan - Green transformation
        "CNY": 65.0,       # China - Carbon neutrality program
        "CHF": 9.0,        # Switzerland - Energy strategy
        "SEK": 85.0,       # Sweden - Green deal
        "NOK": 90.0,       # Norway - Energy transition
        "DKK": 60.0,       # Denmark - Green investment
        "BRL": 50.0,       # Brazil - Green development
        "MXN": 200.0,      # Mexico - Clean energy transition
        "KRW": 12000.0,    # South Korea - Green New Deal
        "SGD": 14.0,       # Singapore - Green finance
        "AED": 36.0,       # UAE - Clean energy strategy
        "SAR": 37.5,       # Saudi Arabia - Green initiative
        "RUB": 750.0,      # Russia - Energy efficiency
        "TRY": 85.0,       # Turkey - Green development
        "PLN": 40.0,       # Poland - Energy transition
        "CZK": 220.0,      # Czech Republic - Green deal
        "HUF": 3500.0,     # Hungary - Green transition
        "THB": 330.0,      # Thailand - Energy transition
        "MYR": 42.0,       # Malaysia - Green technology
        "IDR": 145000.0,   # Indonesia - Energy transition
        "PHP": 500.0,      # Philippines - Clean energy
        "VND": 230000.0,   # Vietnam - Green development
        "EGP": 155.0,      # Egypt - Renewable energy
        "NGN": 4100.0,     # Nigeria - Energy transition
        "KES": 1100.0,     # Kenya - Green energy
        "GHS": 60.0,       # Ghana - Renewable energy
        "NZD": 16.0,       # New Zealand - Climate action
        "ILS": 32.0,       # Israel - Green innovation
        "RON": 42.0,       # Romania - Green transition
        "BGN": 17.0,       # Bulgaria - Energy efficiency
        
        "Unknown": 10.0    # Default fallback
    }
    
    # Currency Exchange API configuration
    EXCHANGE_API_BASE_URL = "https://api.exchangerate-api.com/v4/latest/"
    FALLBACK_EXCHANGE_RATES = {
        "USD": 1.0,
        "EUR": 0.85,
        "GBP": 0.73,
        "JPY": 110.0,
        "INR": 74.5,
        "ZAR": 14.8,
        "CAD": 1.25,
        "AUD": 1.35,
        "CHF": 0.92,
        "CNY": 6.45
    }
    
    def __init__(self):
        """Initialize the financial metrics calculator"""
        logger.info("✅ Financial Metrics Calculator initialized")
        logger.info(f"🌍 Depreciation rates configured for {len(self.DEPRECIATION_RATES)} countries")
        logger.info(f"🏛️ Tax rates configured for {len(self.TAX_RATES)} countries")
        logger.info(f"💰 Transition credits configured for {len(self.TRANSITION_CREDITS)} currencies")
        logger.info(f"💱 Currency exchange API configured: {self.EXCHANGE_API_BASE_URL}")

        # Initialize OpenAI client for notes-based debt extraction
        if openai:
            try:
                self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
                logger.info("🤖 OpenAI client initialized for enhanced debt extraction")
            except Exception as e:
                logger.warning(f"⚠️ OpenAI client initialization failed: {e}")
                self.openai_client = None
        else:
            self.openai_client = None
            logger.warning("⚠️ OpenAI not available - using basic debt extraction")

    def _encode_image_to_base64(self, image_path: str) -> str:
        """Encode image to base64 for OpenAI Vision API"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"❌ Error encoding image {image_path}: {e}")
            return ""

    async def _call_openai_vision(self, images: List[str], prompt: str, max_tokens: int = 4000) -> str:
        """Call OpenAI Vision API with multiple images"""
        if not self.openai_client:
            logger.error("❌ OpenAI client not available")
            return ""

        try:
            # Prepare image content
            content = [{"type": "text", "text": prompt}]

            for image_path in images:
                if os.path.exists(image_path):
                    base64_image = self._encode_image_to_base64(image_path)
                    if base64_image:
                        content.append({
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}",
                                "detail": "high"
                            }
                        })

            # Make API call
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[{"role": "user", "content": content}],
                max_tokens=max_tokens,
                temperature=0.1
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"❌ OpenAI Vision API call failed: {e}")
            return ""

    def get_exchange_rate(self, from_currency: str, to_currency: str = "USD") -> float:
        """
        Get exchange rate from API with fallback to static rates
        """
        logger.info(f"💱 Getting exchange rate: {from_currency} to {to_currency}")
        
        try:
            # Try to get from API
            url = f"{self.EXCHANGE_API_BASE_URL}{from_currency}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if to_currency in data.get("rates", {}):
                    exchange_rate = data["rates"][to_currency]
                    logger.info(f"✅ API exchange rate: 1 {from_currency} = {exchange_rate} {to_currency}")
                    return exchange_rate
                else:
                    logger.warning(f"⚠️ {to_currency} not found in API response")
            else:
                logger.warning(f"⚠️ API request failed with status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            logger.warning(f"⚠️ API request failed: {e}")
        except Exception as e:
            logger.warning(f"⚠️ Exchange rate API error: {e}")
        
        # Fallback to static rates
        logger.info("📊 Using fallback exchange rates")
        
        from_rate = self.FALLBACK_EXCHANGE_RATES.get(from_currency, 1.0)
        to_rate = self.FALLBACK_EXCHANGE_RATES.get(to_currency, 1.0)
        
        if from_currency == to_currency:
            exchange_rate = 1.0
        else:
            # Convert through USD
            exchange_rate = to_rate / from_rate
        
        logger.info(f"📊 Fallback exchange rate: 1 {from_currency} = {exchange_rate} {to_currency}")
        return exchange_rate

    def convert_currency(self, amount: float, from_currency: str, to_currency: str = "USD") -> Dict[str, Any]:
        """
        Convert currency amount with metadata
        """
        if from_currency == to_currency:
            return {
                "original_amount": amount,
                "converted_amount": amount,
                "from_currency": from_currency,
                "to_currency": to_currency,
                "exchange_rate": 1.0,
                "conversion_timestamp": datetime.now().isoformat(),
                "rate_source": "no_conversion_needed"
            }
        
        exchange_rate = self.get_exchange_rate(from_currency, to_currency)
        converted_amount = amount * exchange_rate
        
        return {
            "original_amount": amount,
            "converted_amount": converted_amount,
            "from_currency": from_currency,
            "to_currency": to_currency,
            "exchange_rate": exchange_rate,
            "conversion_timestamp": datetime.now().isoformat(),
            "rate_source": "api_or_fallback"
        }

    def calculate_financial_metrics(self, balance_sheet_data: Dict[str, Any], 
                                  profit_loss_data: Dict[str, Any], 
                                  notes_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Calculate comprehensive financial metrics from extracted data - ENHANCED with Notes Support
        """
        logger.info("🧮 Starting enhanced financial metrics calculation...")
        
        try:
            # Extract balance sheet values
            bs = balance_sheet_data.get("balance_sheet", {})
            pl = profit_loss_data.get("profit_loss", {})
            
            # Basic extracted values with notes support
            extracted_values = self._extract_basic_values(bs, pl, notes_data)
            
            # Calculate debt metrics
            debt_metrics = self._calculate_debt_metrics(extracted_values)
            
            # Calculate WACC components
            wacc_metrics = self._calculate_wacc_metrics(extracted_values, debt_metrics)
            
            # Get country-specific rates (enhanced)
            country_rates = self._get_country_specific_rates(bs)
            
            # Calculate working capital interest with enhanced logic
            working_capital_interest = self._calculate_working_capital_interest_enhanced(extracted_values, wacc_metrics)
            
            # Get currency information
            currency_info = self._get_currency_information(bs)
            
            # Assemble final financial metrics
            financial_metrics = {
                "calculation_metadata": {
                    "calculation_timestamp": datetime.now().isoformat(),
                    "calculator_version": "1.1.0",  # Updated version
                    "extracted_country": bs.get("country", "Unknown"),
                    "extracted_currency": bs.get("currency", "Unknown"),
                    "multiplier_bs": bs.get("multiplier_detected", "unknown"),
                    "multiplier_pl": pl.get("multiplier_detected", "unknown"),
                    "countries_supported": len(self.DEPRECIATION_RATES),
                    "currencies_supported": len(self.TRANSITION_CREDITS)
                },
                
                # Company identification
                "plant_name": bs.get("plant_name", "Unknown Company"),
                "extracted_country": bs.get("country", "Unknown"),
                "extracted_currency": bs.get("currency", "Unknown"),
                
                # Currency information
                "currency_information": currency_info,
                
                # Balance sheet values (enhanced mapping)
                "gross_block": extracted_values["gross_block"],  # Enhanced with notes data
                "wdv_it": extracted_values["ppe"],  # PPE remains as WDV (Written Down Value)
                
                # Fixed assumptions
                "annual_increase_rate": 1.05,  # 5% annual increase
                "salvage_rate": 0.1,           # 10% salvage rate
                
                # Country-specific rates (enhanced)
                "corporate_tax_rate_static": country_rates["tax_rate"],
                "depreciation_rate": country_rates["depreciation_rate"],
                "transition_credit_base": country_rates["transition_credit"],
                
                # Calculated WACC breakdown
                "wacc_breakup": {
                    "cost_of_debt": wacc_metrics["cost_of_debt"],
                    "debt": debt_metrics["debt_share"],
                    "equity": debt_metrics["equity_share"],
                    "roe": wacc_metrics["roe"],
                    "tax_rate": country_rates["tax_rate"],
                    "wacc": wacc_metrics["wacc"]
                },
                
                # Working capital
                "working_capital_interest": working_capital_interest,
                
                # Raw extracted data for reference
                "raw_balance_sheet": bs,
                "raw_profit_loss": pl,
                
                # Calculated intermediates
                "calculated_values": {
                    "total_debt": debt_metrics["total_debt"],
                    "debt_equity_ratio": debt_metrics["debt_equity_ratio"],
                    "equity_multiplier": debt_metrics["equity_multiplier"]
                }
            }
            
            # Log calculation summary
            self._log_calculation_summary(financial_metrics)
            
            logger.info("✅ Financial metrics calculation completed successfully")
            return financial_metrics
            
        except Exception as e:
            logger.error(f"❌ Financial metrics calculation failed: {e}")
            raise Exception(f"Calculation failed: {e}")
    
    def calculate_financial_metrics_with_multi_year_roe(self, balance_sheet_data: Dict[str, Any], 
                                                      profit_loss_data: Dict[str, Any], 
                                                      notes_data: Dict[str, Any] = None,
                                                      output_dir: str = None) -> Dict[str, Any]:
        """
        Calculate comprehensive financial metrics using multi-year geometric mean ROE for assumptions
        """
        logger.info("🧮 Starting financial metrics calculation with multi-year ROE...")
        
        try:
            # Extract balance sheet values
            bs = balance_sheet_data.get("balance_sheet", {})
            pl = profit_loss_data.get("profit_loss", {})
            
            # Set current year for multi-year ROE calculation
            self._current_year = bs.get("year")
            
            # Basic extracted values with notes support
            extracted_values = self._extract_basic_values(bs, pl, notes_data)
            
            # Calculate debt metrics
            debt_metrics = self._calculate_debt_metrics(extracted_values)
            
            # Calculate WACC components with multi-year ROE
            wacc_metrics = self._calculate_wacc_metrics_with_multi_year_roe(extracted_values, debt_metrics, output_dir)
            
            # Get country-specific rates (enhanced)
            country_rates = self._get_country_specific_rates(bs)
            
            # Calculate working capital interest with enhanced logic
            working_capital_interest = self._calculate_working_capital_interest_enhanced(extracted_values, wacc_metrics)
            
            # Get currency information
            currency_info = self._get_currency_information(bs)
            
            # Assemble final financial metrics
            financial_metrics = {
                "calculation_metadata": {
                    "calculation_timestamp": datetime.now().isoformat(),
                    "calculator_version": "1.2.0",  # Updated version for multi-year ROE
                    "extracted_country": bs.get("country", "Unknown"),
                    "extracted_currency": bs.get("currency", "Unknown"),
                    "multiplier_bs": bs.get("multiplier_detected", "unknown"),
                    "multiplier_pl": pl.get("multiplier_detected", "unknown"),
                    "countries_supported": len(self.DEPRECIATION_RATES),
                    "currencies_supported": len(self.TRANSITION_CREDITS),
                    "roe_calculation_method": "multi_year_geometric_mean"
                },
                
                # Company identification
                "plant_name": bs.get("plant_name", "Unknown Company"),
                "extracted_country": bs.get("country", "Unknown"),
                "extracted_currency": bs.get("currency", "Unknown"),
                
                # Currency information
                "currency_information": currency_info,
                
                # Balance sheet values (enhanced mapping)
                "gross_block": extracted_values["gross_block"],  # Enhanced with notes data
                "wdv_it": extracted_values["ppe"],  # PPE remains as WDV (Written Down Value)
                
                # Fixed assumptions
                "annual_increase_rate": 1.05,  # 5% annual increase
                "salvage_rate": 0.1,           # 10% salvage rate
                
                # Country-specific rates (enhanced)
                "corporate_tax_rate_static": country_rates["tax_rate"],
                "depreciation_rate": country_rates["depreciation_rate"],
                "transition_credit_base": country_rates["transition_credit"],
                
                # Calculated WACC breakdown (with multi-year ROE)
                "wacc_breakup": {
                    "cost_of_debt": wacc_metrics["cost_of_debt"],
                    "debt": debt_metrics["debt_share"],
                    "equity": debt_metrics["equity_share"],
                    "roe": wacc_metrics["roe"],  # This is now geometric mean ROE
                    "tax_rate": country_rates["tax_rate"],
                    "wacc": wacc_metrics["wacc"]
                },
                
                # Working capital
                "working_capital_interest": working_capital_interest,
                
                # Raw extracted data for reference
                "raw_balance_sheet": bs,
                "raw_profit_loss": pl,
                
                # Calculated intermediates
                "calculated_values": {
                    "total_debt": debt_metrics["total_debt"],
                    "debt_equity_ratio": debt_metrics["debt_equity_ratio"],
                    "equity_multiplier": debt_metrics["equity_multiplier"]
                }
            }
            
            # Log calculation summary
            self._log_calculation_summary(financial_metrics)
            
            logger.info("✅ Financial metrics calculation with multi-year ROE completed successfully")
            return financial_metrics
            
        except Exception as e:
            logger.error(f"❌ Financial metrics calculation with multi-year ROE failed: {e}")
            raise Exception(f"Multi-year ROE calculation failed: {e}")
            
            # Calculate working capital interest with enhanced logic
            working_capital_interest = self._calculate_working_capital_interest_enhanced(extracted_values, wacc_metrics)
            
            # Get currency information
            currency_info = self._get_currency_information(bs)
            
            # Assemble final financial metrics
            financial_metrics = {
                "calculation_metadata": {
                    "calculation_timestamp": datetime.now().isoformat(),
                    "calculator_version": "1.1.0",  # Updated version
                    "extracted_country": bs.get("country", "Unknown"),
                    "extracted_currency": bs.get("currency", "Unknown"),
                    "multiplier_bs": bs.get("multiplier_detected", "unknown"),
                    "multiplier_pl": pl.get("multiplier_detected", "unknown"),
                    "countries_supported": len(self.DEPRECIATION_RATES),
                    "currencies_supported": len(self.TRANSITION_CREDITS)
                },
                
                # Company identification
                "plant_name": bs.get("plant_name", "Unknown Company"),
                "extracted_country": bs.get("country", "Unknown"),
                "extracted_currency": bs.get("currency", "Unknown"),
                
                # Currency information
                "currency_information": currency_info,
                
                # Balance sheet values (enhanced mapping)
                "gross_block": extracted_values["gross_block"],  # Enhanced with notes data
                "wdv_it": extracted_values["ppe"],  # PPE remains as WDV (Written Down Value)
                
                # Fixed assumptions
                "annual_increase_rate": 1.05,  # 5% annual increase
                "salvage_rate": 0.1,           # 10% salvage rate
                
                # Country-specific rates (enhanced)
                "corporate_tax_rate_static": country_rates["tax_rate"],
                "depreciation_rate": country_rates["depreciation_rate"],
                "transition_credit_base": country_rates["transition_credit"],
                
                # Calculated WACC breakdown
                "wacc_breakup": {
                    "cost_of_debt": wacc_metrics["cost_of_debt"],
                    "debt": debt_metrics["debt_share"],
                    "equity": debt_metrics["equity_share"],
                    "roe": wacc_metrics["roe"],
                    "tax_rate": country_rates["tax_rate"],
                    "wacc": wacc_metrics["wacc"]
                },
                
                # Working capital
                "working_capital_interest": working_capital_interest,
                
                # Raw extracted data for reference
                "raw_balance_sheet": bs,
                "raw_profit_loss": pl,
                
                # Calculated intermediates
                "calculated_values": {
                    "total_debt": debt_metrics["total_debt"],
                    "debt_equity_ratio": debt_metrics["debt_equity_ratio"],
                    "equity_multiplier": debt_metrics["equity_multiplier"]
                }
            }
            
            # Log calculation summary
            self._log_calculation_summary(financial_metrics)
            
            logger.info("✅ Financial metrics calculation completed successfully")
            return financial_metrics
            
        except Exception as e:
            logger.error(f"❌ Financial metrics calculation failed: {e}")
            raise Exception(f"Calculation failed: {e}")

    def calculate_financial_metrics_enhanced(self, extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate financial metrics from enhanced extraction data (with notes support)
        """
        logger.info("🚀 Starting enhanced financial metrics calculation with notes support...")
        
        try:
            # Extract the different data components - FIXED MAPPING
            # Handle both old and new data structures
            balance_sheet_data = extracted_data.get("balance_sheet_data", {})
            profit_loss_data = extracted_data.get("profit_loss_data", {})
            
            # If new structure, map it correctly
            if not balance_sheet_data and "balance_sheet" in extracted_data:
                balance_sheet_data = {"balance_sheet": extracted_data["balance_sheet"]}
                logger.info("📊 Mapped balance_sheet to balance_sheet_data structure")
                
            if not profit_loss_data and "profit_loss" in extracted_data:
                profit_loss_data = {"profit_loss": extracted_data["profit_loss"]}
                logger.info("📈 Mapped profit_loss to profit_loss_data structure")
            
            notes_data = extracted_data.get("notes_data", {})
            
            # Check if this is enhanced extraction
            extraction_type = extracted_data.get("extraction_type", "standard")
            
            if extraction_type == "enhanced_two_stage":
                logger.info("✅ Enhanced two-stage extraction detected")
                return self.calculate_financial_metrics(balance_sheet_data, profit_loss_data, notes_data)
            else:
                logger.info("📊 Standard extraction detected, using legacy method")
                return self.calculate_financial_metrics(balance_sheet_data, profit_loss_data)
                
        except Exception as e:
            logger.error(f"❌ Enhanced financial metrics calculation failed: {e}")
            # Fallback to standard calculation
            try:
                balance_sheet_data = extracted_data.get("balance_sheet_data", {})
                profit_loss_data = extracted_data.get("profit_loss_data", {})
                
                # Handle mapping for fallback too
                if not balance_sheet_data and "balance_sheet" in extracted_data:
                    balance_sheet_data = {"balance_sheet": extracted_data["balance_sheet"]}
                    
                if not profit_loss_data and "profit_loss" in extracted_data:
                    profit_loss_data = {"profit_loss": extracted_data["profit_loss"]}
                
                return self.calculate_financial_metrics(balance_sheet_data, profit_loss_data)
            except Exception as fallback_error:
                logger.error(f"❌ Fallback calculation also failed: {fallback_error}")
                raise Exception(f"Both enhanced and fallback calculations failed: {e}")

    async def calculate_financial_metrics_with_ai_debt_extraction(self, balance_sheet_data: Dict[str, Any],
                                                               profit_loss_data: Dict[str, Any],
                                                               notes_data: Dict[str, Any] = None,
                                                               primary_images: List[str] = None,
                                                               notes_images: Dict[str, List[str]] = None,
                                                               output_dir: str = None) -> Dict[str, Any]:
        """
        Calculate comprehensive financial metrics using AI-enhanced debt extraction from notes
        This method uses the same logic as single_file_debt_equity_processor.py for debt extraction
        """
        logger.info("🧮 Starting financial metrics calculation with AI-enhanced debt extraction...")

        try:
            # Extract balance sheet values
            bs = balance_sheet_data.get("balance_sheet", {})
            pl = profit_loss_data.get("profit_loss", {})

            # Set current year for multi-year ROE calculation
            self._current_year = bs.get("year")

            # AI-ENHANCED: Use notes-based debt extraction with images
            extracted_values = await self._extract_basic_values_with_ai_debt(
                bs, pl, notes_data, primary_images, notes_images
            )

            # Calculate debt metrics
            debt_metrics = self._calculate_debt_metrics(extracted_values)

            # Calculate WACC components with multi-year ROE
            wacc_metrics = self._calculate_wacc_metrics_with_multi_year_roe(extracted_values, debt_metrics, output_dir)

            # Get country-specific rates (enhanced)
            country_rates = self._get_country_specific_rates(bs)

            # Calculate working capital interest with enhanced logic
            working_capital_interest = self._calculate_working_capital_interest_enhanced(extracted_values, wacc_metrics)

            # Get currency information
            currency_info = self._get_currency_information(bs)

            # Assemble final financial metrics
            financial_metrics = {
                "calculation_metadata": {
                    "calculation_timestamp": datetime.now().isoformat(),
                    "calculator_version": "1.3.0",  # Updated version for AI debt extraction
                    "extraction_method": "ai_enhanced_debt_from_notes",
                    "debt_extraction_source": "notes_based_ai_analysis",
                    "cost_of_debt_logic": "zero_debt_aware"
                },

                # Basic financial data
                "ppe": extracted_values["ppe"],
                "gross_block": extracted_values["gross_block"],
                "intangible_assets": extracted_values["intangible_assets"],
                "current_assets": extracted_values["current_assets"],
                "current_liabilities": extracted_values["current_liabilities"],
                "total_equity": extracted_values["total_equity"],
                "cash": extracted_values["cash"],
                "revenue": extracted_values["revenue"],
                "finance_cost": extracted_values["finance_cost"],
                "profit_before_tax": extracted_values["profit_before_tax"],
                "net_income": extracted_values["net_income"],

                # Debt metrics (AI-enhanced)
                "short_term_debt": extracted_values["short_term_debt"],
                "long_term_debt": extracted_values["long_term_debt"],
                "total_debt": extracted_values["total_debt"],

                # Country-specific rates (enhanced)
                "corporate_tax_rate_static": country_rates["tax_rate"],
                "depreciation_rate": country_rates["depreciation_rate"],
                "transition_credit_base": country_rates["transition_credit"],

                # Calculated WACC breakdown (with AI-enhanced debt)
                "wacc_breakup": {
                    "cost_of_debt": wacc_metrics["cost_of_debt"],
                    "debt": debt_metrics["debt_share"],
                    "equity": debt_metrics["equity_share"],
                    "roe": wacc_metrics["roe"],  # This is now geometric mean ROE
                    "tax_rate": country_rates["tax_rate"],
                    "wacc": wacc_metrics["wacc"]
                },

                # Enhanced metrics
                "working_capital_interest": working_capital_interest,
                "debt_equity_ratio": debt_metrics["debt_equity_ratio"],
                "country": extracted_values["country"],
                "currency": extracted_values["currency"],
                "currency_info": currency_info
            }

            # Calculate final WACC
            final_wacc = self._calculate_final_wacc(wacc_metrics, debt_metrics, country_rates)
            financial_metrics["wacc_breakup"]["wacc"] = final_wacc

            # Log calculation summary
            self._log_calculation_summary(financial_metrics)

            logger.info("✅ AI-enhanced financial metrics calculation completed successfully")
            return financial_metrics

        except Exception as e:
            logger.error(f"❌ AI-enhanced financial metrics calculation failed: {e}")
            # Fallback to standard calculation
            try:
                logger.info("🔄 Falling back to standard calculation...")
                return self.calculate_financial_metrics_with_multi_year_roe(
                    balance_sheet_data, profit_loss_data, notes_data, output_dir
                )
            except Exception as fallback_error:
                logger.error(f"❌ Fallback calculation also failed: {fallback_error}")
                raise Exception(f"Both AI-enhanced and fallback calculations failed: {e}")

    async def get_debt_notes_adaptive(self, primary_images: List[str], statement_type: str) -> List[str]:
        """🎯 ADAPTIVE: Get important debt-related notes from financial statements for assumption extraction"""
        logger.info(f"🎯 Adaptive Debt Note Selection: Getting debt-related notes from {statement_type}...")

        if not self.openai_client:
            logger.warning("⚠️ OpenAI not available, using fallback notes")
            return ['13', '14', '15', '16', '17', '18']

        smart_notes_prompt = f"""
        Analyze this {statement_type} and identify the most important note numbers for debt extraction and financial assumptions.

        **PRIORITY ITEMS TO FIND NOTES FOR:**
        - Long-term borrowings/debt (Non-current borrowings, Amounts owed to Group undertakings)
        - Short-term borrowings/debt (Current borrowings, Creditors amounts falling due within one year)
        - Interest rates and borrowing terms
        - Loan agreements and facilities
        - Bank borrowings and financial institutions
        - Secured and unsecured debt details
        - Interest bearing loans and advances

        Look for note references next to these items in the financial statements.

        Return the 6-8 most important note numbers that contain detailed debt and borrowing information.

        Return ONLY this JSON format:
        {{
            "important_notes": ["13", "14", "15", "16", "17", "18"],
            "reasoning": "Notes identified from {statement_type} for debt extraction"
        }}

        Extract only the note NUMBERS (like "13", "14").
        """

        try:
            response = await self._call_openai_vision(primary_images, smart_notes_prompt, max_tokens=1000)

            if response:
                import re
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    notes_data = json.loads(json_match.group())
                    final_notes = notes_data.get("important_notes", [])

                    logger.info(f"🎯 Adaptive debt note selection from {statement_type}: {final_notes}")

                    if final_notes:
                        return final_notes

            # Fallback to common debt-related notes
            fallback_notes = ['13', '14', '15', '16', '17', '18']
            logger.info("⚠️ Using fallback debt notes")
            return fallback_notes

        except Exception as e:
            logger.error(f"Error in adaptive debt note selection from {statement_type}: {e}")
            fallback_notes = ['13', '14', '15', '16', '17', '18']
            logger.info(f"⚠️ Using fallback debt notes: {fallback_notes}")
            return fallback_notes

    def get_assumption_debt_prompt(self, statement_type: str, currency: str, important_notes: List[str]) -> str:
        """🌍 DEBT EXTRACTION PROMPT: Extract debt information from notes for financial assumptions"""

        base_instructions = f"""
        🌍 DEBT EXTRACTION FOR FINANCIAL ASSUMPTIONS WITH UNIT DETECTION

        🚨 CRITICAL INSTRUCTION FOR UNIT DETECTION:
        - Look for unit declarations in document headers like:
          * "(All amounts are in ₹ Crores, unless otherwise specified)" → extract "Crores"
          * "Figures in ₹ Lakhs" → extract "Lakhs"
          * "Amount in USD Millions" → extract "Millions"
          * "Currency in ₹ Thousands" → extract "Thousands"
        - SPECIAL PATTERN: When you see currency symbols followed by zeros (£000, $000, €000):
          * Count the zeros: 3 zeros = "Thousands", 6 zeros = "Millions", 9 zeros = "Billions"
        - Return ONLY the unit name in currency_unit field (NOT the currency symbol)
        - Extract all numbers exactly as they appear WITHOUT conversion

        IMPORTANT CONTEXT:
        - Currency: {currency}
        - Intelligently selected debt notes for analysis: {important_notes}
        - PRIMARY SOURCE: {statement_type}
        - Extract raw numbers without any mathematical conversion
        """

        specific_instructions = f"""
        🚨 CRITICAL DEBT EXTRACTION APPROACH FOR ASSUMPTIONS:

        **EXTRACTION PRIORITY:**
        - LONG-TERM DEBT EXTRACTION PRIORITY:
          1. First Priority: Extract from Notes section of "Amounts owed to Group undertakings"
          2. Second Priority: "Non-current borrowings" from Balance Sheet
          3. If neither available: null
        - SHORT-TERM DEBT EXTRACTION PRIORITY:
          1. First Priority: Extract from Notes section of "Creditors: amounts falling due within one year"
          2. Second Priority: "Current borrowings" from Balance Sheet
          3. If neither available: null
        - BORROWING FOCUS: Extract ONLY borrowing/loan items for debt analysis
        - EXCLUSIONS: DO NOT include trade payables, trade creditors, supplier dues, vendor payments, or other non-borrowing liabilities in debt analysis

        🚨 CRITICAL NOTES-BASED EXTRACTION APPROACH:

        **NOTES SECTION SOURCE → Detailed Debt Information:**
        - long_term_debt.total_value = Total from notes section (e.g., "Group undertakings: 500M")
        - long_term_debt.secured_debt[] = Individual secured borrowing items from notes
        - long_term_debt.unsecured_debt[] = Individual unsecured borrowing items from notes
        - short_term_debt.total_value = Total from notes section (e.g., "Current creditors: 200M")
        - short_term_debt.secured_debt[] = Individual secured borrowing items from notes
        - short_term_debt.unsecured_debt[] = Individual unsecured borrowing items from notes
        - interest_rates = Extract interest rate information from notes

        **CRITICAL RULES:**
        - ALWAYS prioritize notes section for detailed debt breakdown
        - ALWAYS use notes section for individual borrowing items when available
        - If notes not available, extract from balance sheet line items
        - DO NOT return null values - always extract available data
        - Extract numbers exactly as they appear in the document
        - Extract ONLY borrowing/loan items (NOT trade payables, creditors, or other non-borrowing items)

        1. LONG-TERM DEBT ANALYSIS - NOTES-BASED EXTRACTION:
        - **PRIMARY SOURCE**: Extract from Notes section of "Amounts owed to Group undertakings"
        - **EXTRACTION APPROACH:**
          * Look for detailed breakdown in notes showing individual borrowing items
          * Extract ONLY borrowing/loan items (NOT trade payables, creditors, or other non-borrowing items)
          * secured_debt.total = Total secured borrowings from Group undertakings notes
          * unsecured_debt.total = Total unsecured borrowings from Group undertakings notes
        - **DETAILED BREAKDOWN:**
          * secured_debt.details[] = Individual secured borrowing items from notes
          * unsecured_debt.details[] = Individual unsecured borrowing items from notes
        - **FALLBACK**: If "Amounts owed to Group undertakings" notes unavailable, use "Non-current borrowings" from Balance Sheet
        - Extract numbers exactly as they appear in document

        2. SHORT-TERM DEBT ANALYSIS - NOTES-BASED EXTRACTION:
        - **PRIMARY SOURCE**: Extract from Notes section of "Creditors: amounts falling due within one year"
        - **EXTRACTION APPROACH:**
          * Look for detailed breakdown in notes showing individual items
          * Extract ONLY borrowing/loan items (NOT trade creditors, accrued expenses, or other non-borrowing items)
          * secured_debt.total = Total secured borrowings from Creditors notes
          * unsecured_debt.total = Total unsecured borrowings + current maturities from Creditors notes
        - **DETAILED BREAKDOWN:**
          * secured_debt.details[] = Individual secured borrowing items from notes
          * unsecured_debt.details[] = Individual unsecured borrowing items from notes
        - **FALLBACK**: If "Creditors: amounts falling due within one year" notes unavailable, use "Current borrowings" from Balance Sheet
        - Extract interest rates for short-term facilities from notes
        - Extract numbers exactly as they appear in document

        3. INTEREST RATE EXTRACTION:
        - Extract interest rates from notes sections
        - Look for borrowing terms and conditions
        - Extract both range (e.g., "8.5-9.5%") and specific rates
        - Include currency and tenure information if available

        🚨 CRITICAL EXTRACTION SOURCES:
        - LONG-TERM DEBT: Extract from "Amounts owed to Group undertakings" notes (borrowing items only)
        - SHORT-TERM DEBT: Extract from "Creditors: amounts falling due within one year" notes (borrowing items only)

        If any information is not available, return as null.

        Return as JSON with RAW NUMBERS and UNIT INFORMATION:
        {{
            "company_info": {{
                "name": "Company Name",
                "date": "31/03/2024",
                "year": "2024"
            }},
            "currency_unit": "Crores",
            "long_term_debt": {{
                "total_value": 2778.54,
                "interest_rates": "8.5-9.5%",
                "secured_debt": {{
                    "total": 2778.54,
                    "details": [
                        {{"name": "Non-current borrowings", "amount": 2778.54, "lender": "Banks/Financial Institutions", "security": "secured"}}
                    ]
                }},
                "unsecured_debt": {{
                    "total": 0,
                    "details": []
                }}
            }},
            "short_term_debt": {{
                "total_value": 1079.20,
                "interest_rates": "8.0-9.0%",
                "current_borrowings": 836.10,
                "current_maturities": 243.10,
                "secured_debt": {{
                    "total": 836.10,
                    "details": [
                        {{"name": "Working Capital Loan", "amount": 400.00, "lender": "Bank A", "security": "secured"}},
                        {{"name": "Cash Credit Facility", "amount": 300.00, "lender": "Bank B", "security": "secured"}}
                    ]
                }},
                "unsecured_debt": {{
                    "total": 243.10,
                    "details": [
                        {{"name": "Current maturities of long-term debt", "amount": 243.10, "lender": "Various", "security": "unsecured"}}
                    ]
                }}
            }}
        }}

        🎯 EXTRACT RAW NUMBERS AND UNIT INFORMATION!
        Look for declarations like "All amounts are in ₹Crores" and return "Crores" in currency_unit field!
        Extract all numbers exactly as they appear in the document!
        """

        return f"""
        You are analyzing financial statements to extract comprehensive debt information for financial assumptions with maximum accuracy.
        You MUST detect unit information and extract raw numbers without conversion!

        {base_instructions}

        {specific_instructions}

        🚨 MANDATORY REQUIREMENTS:
        - ALWAYS use Company columns when both Group and Company are present
        - ALWAYS format dates as DD/MM/YYYY
        - ALWAYS extract ENDING YEAR from date ranges (2024 from "2023-24")
        - ALWAYS extract DETAILED INDIVIDUAL VALUES from notes OR balance sheet - NEVER return null
        - 🚨 DEBT EXTRACTION: Extract long-term debt from "Amounts owed to Group undertakings" notes, short-term debt from "Creditors: amounts falling due within one year" notes (borrowing items only)
        - BORROWING FOCUS: Extract ONLY borrowing/loan items for debt analysis - DO NOT include trade payables, trade creditors, or other non-borrowing liabilities
        - If notes not available, extract from balance sheet line items
        - Focus on current year data from Company column only
        - Extract all numbers exactly as they appear in the document WITHOUT conversion

        Never return null - extract from balance sheet if notes unavailable!
        Extract raw numbers without any mathematical conversion!
        Use EXACT field names as shown in the JSON example above!
        """

    async def extract_debt_data_for_assumptions(self, primary_images: List[str], notes_images: Dict[str, List[str]],
                                              currency: str, statement_type: str) -> Dict:
        """🌍 AI-POWERED DEBT EXTRACTION: Extract debt data from notes for financial assumptions"""
        logger.info(f"🌍 AI-powered debt extraction for assumptions from {statement_type}...")

        if not self.openai_client:
            logger.warning("⚠️ OpenAI not available, returning empty debt data")
            return {}

        try:
            # Get important debt-related notes adaptively
            important_notes = await self.get_debt_notes_adaptive(primary_images, statement_type)

            # Generate AI prompt for debt extraction
            prompt = self.get_assumption_debt_prompt(statement_type, currency, important_notes)

            # Prepare all images for analysis
            all_images = primary_images.copy()

            # Add notes images if available
            for note_num in important_notes:
                if note_num in notes_images:
                    all_images.extend(notes_images[note_num])
                    logger.info(f"📝 Added note {note_num} images for debt analysis")

            logger.info(f"🔍 Analyzing {len(all_images)} images for debt extraction")

            # Call OpenAI Vision API
            response = await self._call_openai_vision(all_images, prompt, max_tokens=4000)

            if response:
                # Extract JSON from response
                import re
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    extraction_data = json.loads(json_match.group())
                    logger.info("✅ Debt extraction completed successfully")
                    logger.info(f"🔍 Extracted debt data keys: {list(extraction_data.keys())}")
                    return extraction_data
                else:
                    logger.error("❌ No valid JSON found in AI response")
                    return {}
            else:
                logger.error("❌ Empty response from AI")
                return {}

        except Exception as e:
            logger.error(f"❌ Error extracting debt data for assumptions: {e}")
            return {}

    def safe_numeric_value(self, value) -> Optional[float]:
        """Safely convert value to numeric, handling None and string cases"""
        if value is None:
            return None

        if isinstance(value, (int, float)):
            return float(value)

        if isinstance(value, str):
            try:
                # Remove commas and convert
                cleaned = value.replace(",", "").strip()
                if cleaned == "" or cleaned.lower() == "null":
                    return None
                return float(cleaned)
            except (ValueError, AttributeError):
                return None

        return None

    async def _extract_basic_values_with_ai_debt(self, bs: Dict[str, Any], pl: Dict[str, Any],
                                               notes_data: Dict[str, Any] = None,
                                               primary_images: List[str] = None,
                                               notes_images: Dict[str, List[str]] = None) -> Dict[str, Any]:
        """Extract and validate basic financial values - AI-ENHANCED VERSION with Notes-Based Debt Extraction"""
        logger.info("📊 Extracting basic financial values with AI-enhanced debt extraction...")

        # Balance Sheet values - ENHANCED: Safe numeric conversion
        ppe = self._safe_numeric_conversion(bs.get("Property, plant and equipment", 0))
        intangible_assets = self._safe_numeric_conversion(bs.get("intangible_assets", 0))
        current_assets = self._safe_numeric_conversion(bs.get("current_assets", 0))
        current_liabilities = self._safe_numeric_conversion(bs.get("current_liabilities", 0))
        total_equity = self._safe_numeric_conversion(bs.get("total_equity", 0))
        cash = self._safe_numeric_conversion(bs.get("cash_and_cash_equivalents", 0))

        # Country and currency detection
        country = bs.get("country", "Unknown")
        currency = bs.get("currency", "Unknown")

        # AI-ENHANCED DEBT EXTRACTION: Use notes-based extraction if images available
        short_term_debt = 0
        long_term_debt = 0

        if primary_images and notes_images and self.openai_client:
            logger.info("🤖 Using AI-enhanced debt extraction from notes...")
            try:
                # Extract debt data using AI from notes
                statement_type = "Statement of Balance Sheet"  # Default assumption
                debt_data = await self.extract_debt_data_for_assumptions(
                    primary_images, notes_images, currency, statement_type
                )

                if debt_data:
                    # Extract long-term debt
                    lt_debt_info = debt_data.get("long_term_debt", {})
                    lt_total = self.safe_numeric_value(lt_debt_info.get("total_value"))
                    if lt_total is not None and lt_total > 0:
                        long_term_debt = lt_total
                        logger.info(f"🤖 AI extracted long-term debt: {long_term_debt:,.0f}")

                    # Extract short-term debt
                    st_debt_info = debt_data.get("short_term_debt", {})
                    st_total = self.safe_numeric_value(st_debt_info.get("total_value"))
                    if st_total is not None and st_total > 0:
                        short_term_debt = st_total
                        logger.info(f"🤖 AI extracted short-term debt: {short_term_debt:,.0f}")

                    # Also try current_borrowings as fallback
                    if short_term_debt == 0:
                        st_current = self.safe_numeric_value(st_debt_info.get("current_borrowings"))
                        if st_current is not None and st_current > 0:
                            short_term_debt = st_current
                            logger.info(f"🤖 AI extracted short-term debt (current borrowings): {short_term_debt:,.0f}")

            except Exception as e:
                logger.error(f"❌ AI debt extraction failed: {e}")
                logger.info("🔄 Falling back to balance sheet debt extraction...")

        # FALLBACK: Use balance sheet values if AI extraction failed or not available
        if short_term_debt == 0:
            short_term_debt = self._safe_numeric_conversion(bs.get("short_term_borrowings", 0))
            logger.info(f"📊 Using balance sheet short-term debt: {short_term_debt:,.0f}")

        if long_term_debt == 0:
            long_term_debt = self._safe_numeric_conversion(bs.get("Long_Term_borrowing", 0))
            logger.info(f"📊 Using balance sheet long-term debt: {long_term_debt:,.0f}")

        # Enhanced borrowings extraction from traditional notes if available (backward compatibility)
        if notes_data and "notes_data" in notes_data:
            notes_info = notes_data["notes_data"]

            # Use notes data for borrowings if available and AI extraction didn't work
            notes_short_term = self._safe_numeric_conversion(notes_info.get("short_term_borrowings_detail", 0))
            notes_long_term = self._safe_numeric_conversion(notes_info.get("long_term_borrowings_detail", 0))

            if notes_short_term > 0 and short_term_debt == 0:
                short_term_debt = notes_short_term
                logger.info(f"📝 Using short-term borrowings from traditional notes: {short_term_debt:,.0f}")

            if notes_long_term > 0 and long_term_debt == 0:
                long_term_debt = notes_long_term
                logger.info(f"📝 Using long-term borrowings from traditional notes: {long_term_debt:,.0f}")

        # Interest bearing loans fallback
        interest_bearing_loans = self._safe_numeric_conversion(bs.get("interest_bearing_loans_borrowings", 0))

        # Profit & Loss values - ENHANCED: Safe numeric conversion with fallback logic
        revenue = self._safe_numeric_conversion(pl.get("revenue", 0))
        turnover = self._safe_numeric_conversion(pl.get("turnover", 0))
        finance_cost = self._safe_numeric_conversion(pl.get("finance_cost", 0))
        interest_payable = self._safe_numeric_conversion(pl.get("interest_payable_similar_expenses", 0))

        # DEBUG: Log what we actually received from P&L extraction
        logger.info(f"🔍 DEBUG P&L Values Received:")
        logger.info(f"  📊 finance_cost (raw): {pl.get('finance_cost', 'NOT_FOUND')}")
        logger.info(f"  📊 interest_payable_similar_expenses (raw): {pl.get('interest_payable_similar_expenses', 'NOT_FOUND')}")
        logger.info(f"  📊 finance_cost (converted): {finance_cost}")
        logger.info(f"  📊 interest_payable (converted): {interest_payable}")

        profit_before_tax = self._safe_numeric_conversion(pl.get("profit_before_tax", 0))
        net_income = self._safe_numeric_conversion(pl.get("net_income", 0))

        # Enhanced revenue logic: Priority to Turnover
        if turnover > 0:
            revenue = turnover
            logger.info(f"📈 Using Turnover as revenue: {revenue:,.0f}")
        elif revenue <= 0 and turnover <= 0:
            logger.warning("⚠️ No revenue or turnover found")

        # PRIORITY: Use "Interest payable and similar expenses" FIRST, then finance_cost
        # Handle negative values (expenses are often recorded as negative)
        if interest_payable != 0:
            final_finance_cost = abs(interest_payable)  # Always positive
            logger.info(f"💸 PRIORITY: Using 'Interest payable and similar expenses' as finance cost: {final_finance_cost:,.0f}")
            logger.info(f"💡 Original value was: {interest_payable:,.0f} (converted to positive)")
        elif finance_cost > 0:
            final_finance_cost = finance_cost
            logger.info(f"💸 Using 'Finance cost' as finance cost: {final_finance_cost:,.0f}")
        else:
            final_finance_cost = 0
            logger.warning("⚠️ No finance cost or interest payable found, using 0")

        logger.info(f"🎯 FINAL FINANCE COST DECISION: {final_finance_cost:,.0f}")

        # Calculate total debt - NOW SAFE: 0 + 0 = 0 (not None + None = Error)
        total_debt = short_term_debt + long_term_debt

        # If interest bearing loans is higher, use that
        if interest_bearing_loans > total_debt:
            total_debt = interest_bearing_loans
            logger.info(f"💰 Using interest bearing loans as total debt: {total_debt:,.0f}")

        # Validate critical values
        if ppe <= 0:
            logger.warning("⚠️ PPE is zero or negative, using minimum value")
            ppe = 1000000  # 1 million minimum

        if total_equity <= 0:
            logger.warning("⚠️ Total equity is zero or negative, using minimum value")
            total_equity = 1000000  # 1 million minimum

        # Note: Total debt can legitimately be zero for debt-free companies
        # Only use minimum if we suspect data extraction error (all values are zero)
        if total_debt <= 0 and ppe <= 1000000 and total_equity <= 1000000:
            logger.warning("⚠️ All financial values seem too low, using minimum debt")
            total_debt = 1000000  # 1 million minimum only if everything looks wrong
        elif total_debt <= 0:
            logger.info("ℹ️ Company appears to be debt-free (total debt = 0)")
            total_debt = 0  # Keep as zero for debt-free companies

        # Enhanced gross block calculation
        gross_block = ppe  # Default to PPE as WDV
        if notes_data and "notes_data" in notes_data:
            notes_info = notes_data["notes_data"]
            ppe_cost_valuation = self._safe_numeric_conversion(notes_info.get("ppe_cost_valuation_total", 0))

            if ppe_cost_valuation > 0:
                # Use notes PPE cost/valuation for gross block, keep balance sheet PPE as WDV
                gross_block = ppe_cost_valuation
                logger.info(f"📝 Enhanced gross block from notes: {ppe_cost_valuation:,.0f}")
                logger.info(f"📊 WDV from balance sheet: {ppe:,.0f}")
            else:
                logger.info(f"📊 Using PPE as gross block (no notes data): {gross_block:,.0f}")
        else:
            logger.info(f"📊 Using PPE as gross block (no notes available): {gross_block:,.0f}")

        extracted_values = {
            "ppe": ppe,  # This remains as WDV (Written Down Value)
            "gross_block": gross_block,  # This will be enhanced with notes data
            "intangible_assets": intangible_assets,
            "current_assets": current_assets,
            "current_liabilities": current_liabilities,
            "short_term_debt": short_term_debt,
            "long_term_debt": long_term_debt,
            "total_debt": total_debt,
            "total_equity": total_equity,
            "cash": cash,
            "revenue": revenue,
            "finance_cost": final_finance_cost,
            "profit_before_tax": profit_before_tax,
            "net_income": net_income,
            "country": country,
            "currency": currency,
            "notes_data": notes_data
        }

        logger.info("✅ AI-enhanced basic values extraction completed successfully")
        logger.info(f"💰 Final debt values: ST={short_term_debt:,.0f}, LT={long_term_debt:,.0f}, Total={total_debt:,.0f}")

        return extracted_values

    def _extract_basic_values(self, bs: Dict[str, Any], pl: Dict[str, Any],
                             notes_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Extract and validate basic financial values - ENHANCED VERSION with Notes Support"""
        logger.info("📊 Extracting basic financial values with enhanced logic...")

        # Balance Sheet values - ENHANCED: Safe numeric conversion
        ppe = self._safe_numeric_conversion(bs.get("Property, plant and equipment", 0))
        intangible_assets = self._safe_numeric_conversion(bs.get("intangible_assets", 0))
        current_assets = self._safe_numeric_conversion(bs.get("current_assets", 0))
        current_liabilities = self._safe_numeric_conversion(bs.get("current_liabilities", 0))
        short_term_debt = self._safe_numeric_conversion(bs.get("short_term_borrowings", 0))
        long_term_debt = self._safe_numeric_conversion(bs.get("Long_Term_borrowing", 0))
        interest_bearing_loans = self._safe_numeric_conversion(bs.get("interest_bearing_loans_borrowings", 0))
        total_equity = self._safe_numeric_conversion(bs.get("total_equity", 0))
        cash = self._safe_numeric_conversion(bs.get("cash_and_cash_equivalents", 0))
        
        # Country and currency detection
        country = bs.get("country", "Unknown")
        currency = bs.get("currency", "Unknown")
        
        # Enhanced borrowings extraction from notes if available
        if notes_data and "notes_data" in notes_data:
            notes_info = notes_data["notes_data"]
            
            # Use notes data for borrowings if available
            notes_short_term = self._safe_numeric_conversion(notes_info.get("short_term_borrowings_detail", 0))
            notes_long_term = self._safe_numeric_conversion(notes_info.get("long_term_borrowings_detail", 0))
            
            if notes_short_term > 0:
                short_term_debt = notes_short_term
                logger.info(f"📝 Using short-term borrowings from notes: {short_term_debt:,.0f}")
            
            if notes_long_term > 0:
                long_term_debt = notes_long_term
                logger.info(f"📝 Using long-term borrowings from notes: {long_term_debt:,.0f}")

        # Profit & Loss values - ENHANCED: Safe numeric conversion with fallback logic
        revenue = self._safe_numeric_conversion(pl.get("revenue", 0))
        turnover = self._safe_numeric_conversion(pl.get("turnover", 0))
        finance_cost = self._safe_numeric_conversion(pl.get("finance_cost", 0))
        interest_payable = self._safe_numeric_conversion(pl.get("interest_payable_similar_expenses", 0))
        
        # DEBUG: Log what we actually received from P&L extraction
        logger.info(f"🔍 DEBUG P&L Values Received:")
        logger.info(f"  📊 finance_cost (raw): {pl.get('finance_cost', 'NOT_FOUND')}")
        logger.info(f"  📊 interest_payable_similar_expenses (raw): {pl.get('interest_payable_similar_expenses', 'NOT_FOUND')}")
        logger.info(f"  📊 finance_cost (converted): {finance_cost}")
        logger.info(f"  📊 interest_payable (converted): {interest_payable}")
        logger.info(f"🔍 DEBUG Finance Cost Logic Check:")
        logger.info(f"  📊 interest_payable != 0? {interest_payable != 0}")
        logger.info(f"  📊 finance_cost > 0? {finance_cost > 0}")
        profit_before_tax = self._safe_numeric_conversion(pl.get("profit_before_tax", 0))
        net_income = self._safe_numeric_conversion(pl.get("net_income", 0))
        
        # Enhanced revenue logic: Priority to Turnover
        if turnover > 0:
            revenue = turnover
            logger.info(f"📈 Using Turnover as revenue: {revenue:,.0f}")
        elif revenue <= 0 and turnover <= 0:
            logger.warning("⚠️ No revenue or turnover found")
        
        # PRIORITY: Use "Interest payable and similar expenses" FIRST, then finance_cost
        # Handle negative values (expenses are often recorded as negative)
        if interest_payable != 0:
            final_finance_cost = abs(interest_payable)  # Always positive
            logger.info(f"💸 PRIORITY: Using 'Interest payable and similar expenses' as finance cost: {final_finance_cost:,.0f}")
            logger.info(f"💡 Original value was: {interest_payable:,.0f} (converted to positive)")
        elif finance_cost > 0:
            final_finance_cost = finance_cost
            logger.info(f"💸 Using 'Finance cost' as finance cost: {final_finance_cost:,.0f}")
        else:
            final_finance_cost = 0
            logger.warning("⚠️ No finance cost or interest payable found, using 0")
        
        logger.info(f"🎯 FINAL FINANCE COST DECISION: {final_finance_cost:,.0f}")
        
        # Calculate total debt - NOW SAFE: 0 + 0 = 0 (not None + None = Error)
        total_debt = short_term_debt + long_term_debt
        
        # If interest bearing loans is higher, use that
        if interest_bearing_loans > total_debt:
            total_debt = interest_bearing_loans
            logger.info(f"💰 Using interest bearing loans as total debt: {total_debt:,.0f}")
        
        # Validate critical values
        if ppe <= 0:
            logger.warning("⚠️ PPE is zero or negative, using minimum value")
            ppe = 1000000  # 1 million minimum
        
        if total_equity <= 0:
            logger.warning("⚠️ Total equity is zero or negative, using minimum value")
            total_equity = 1000000  # 1 million minimum
        
        # Note: Total debt can legitimately be zero for debt-free companies
        # Only use minimum if we suspect data extraction error (all values are zero)
        if total_debt <= 0 and ppe <= 1000000 and total_equity <= 1000000:
            logger.warning("⚠️ All financial values seem too low, using minimum debt")
            total_debt = 1000000  # 1 million minimum only if everything looks wrong
        elif total_debt <= 0:
            logger.info("ℹ️ Company appears to be debt-free (total debt = 0)")
            total_debt = 0  # Keep as zero for debt-free companies
        
        # Enhanced gross block calculation
        gross_block = ppe  # Default to PPE as WDV
        if notes_data and "notes_data" in notes_data:
            notes_info = notes_data["notes_data"]
            ppe_cost_valuation = self._safe_numeric_conversion(notes_info.get("ppe_cost_valuation_total", 0))
            
            if ppe_cost_valuation > 0:
                # Use notes PPE cost/valuation for gross block, keep balance sheet PPE as WDV
                gross_block = ppe_cost_valuation
                logger.info(f"📝 Enhanced gross block from notes: {ppe_cost_valuation:,.0f}")
                logger.info(f"📊 WDV from balance sheet: {ppe:,.0f}")
            else:
                logger.info(f"📊 Using PPE as gross block (no notes data): {gross_block:,.0f}")
        else:
            logger.info(f"📊 Using PPE as gross block (no notes available): {gross_block:,.0f}")
        
        extracted_values = {
            "ppe": ppe,  # This remains as WDV (Written Down Value)
            "gross_block": gross_block,  # This will be enhanced with notes data
            "intangible_assets": intangible_assets,
            "current_assets": current_assets,
            "current_liabilities": current_liabilities,
            "short_term_debt": short_term_debt,
            "long_term_debt": long_term_debt,
            "total_debt": total_debt,
            "total_equity": total_equity,
            "cash": cash,
            "revenue": revenue,
            "finance_cost": final_finance_cost,
            "profit_before_tax": profit_before_tax,
            "net_income": net_income,
            "country": country,
            "currency": currency,
            "notes_data": notes_data
        }
        
        logger.info(f"💰 PPE: {ppe:,.0f}")
        logger.info(f"🏦 Total Debt: {total_debt:,.0f}")
        logger.info(f"📈 Total Equity: {total_equity:,.0f}")
        logger.info(f"💸 Finance Cost (FINAL): {final_finance_cost:,.0f}")
        logger.info(f"📊 Net Income: {net_income:,.0f}")
        
        return extracted_values

    def _safe_numeric_conversion(self, value) -> float:
        """Safely convert value to numeric, handling strings and other types"""
        if value is None:
            return 0.0

        if isinstance(value, (int, float)):
            return float(value)

        if isinstance(value, str):
            # Handle special cases
            if value.lower() in ['not_found', 'n/a', 'na', '-', '', 'nil', 'null']:
                return 0.0

            try:
                # Remove common non-numeric characters and currency symbols
                clean_value = value.replace(',', '').replace('(', '-').replace(')', '')
                clean_value = clean_value.replace('₹', '').replace('$', '').replace('€', '').replace('£', '')
                clean_value = ''.join(c for c in clean_value if c.isdigit() or c in '.-')
                return float(clean_value) if clean_value else 0.0
            except (ValueError, TypeError):
                logger.warning(f"⚠️ Could not convert '{value}' to numeric, using 0.0")
                return 0.0

        return 0.0

    def _calculate_debt_metrics(self, values: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate debt-related metrics"""
        logger.info("🏦 Calculating debt metrics...")
        
        total_debt = values["total_debt"]
        total_equity = values["total_equity"]
        total_capital = total_debt + total_equity
        
        # Calculate debt and equity shares
        debt_share = total_debt / total_capital if total_capital > 0 else 0.5
        equity_share = total_equity / total_capital if total_capital > 0 else 0.5
        
        # Ensure shares sum to 1.0
        if debt_share + equity_share != 1.0:
            total_shares = debt_share + equity_share
            if total_shares > 0:
                debt_share = debt_share / total_shares
                equity_share = equity_share / total_shares
            else:
                debt_share = 0.5
                equity_share = 0.5
        
        # Calculate other debt metrics
        debt_equity_ratio = total_debt / total_equity if total_equity > 0 else 1.0
        equity_multiplier = total_capital / total_equity if total_equity > 0 else 2.0
        
        debt_metrics = {
            "total_debt": total_debt,
            "debt_share": debt_share,
            "equity_share": equity_share,
            "debt_equity_ratio": debt_equity_ratio,
            "equity_multiplier": equity_multiplier
        }
        
        logger.info(f"⚖️ Debt Share: {debt_share:.1%}")
        logger.info(f"📈 Equity Share: {equity_share:.1%}")
        logger.info(f"📊 Debt-to-Equity Ratio: {debt_equity_ratio:.2f}")
        
        return debt_metrics

    def get_multi_year_geometric_mean_roe(self, output_dir: str, latest_year: int, lookback_years: int = 3) -> Optional[float]:
        """
        Calculate geometric mean ROE from multiple years of debt_equity files
        
        Args:
            output_dir: Base output directory containing year folders
            latest_year: Latest year to start from
            lookback_years: Number of years to look back (default: 3)
            
        Returns:
            Geometric mean ROE or None if insufficient data
        """
        logger.info(f"📊 Calculating {lookback_years}-year geometric mean ROE starting from {latest_year}...")
        
        try:
            from pathlib import Path
            import json
            
            base_path = Path(output_dir).parent if Path(output_dir).name.startswith("year_") else Path(output_dir)
            
            # Find target years (consecutive years going backwards)
            target_years = [latest_year - i for i in range(lookback_years)]
            logger.info(f"🎯 Target years for ROE calculation: {target_years}")
            
            roe_values = []
            found_years = []
            
            # Search for debt_equity files in year folders
            for year in target_years:
                year_dir = base_path / f"year_{year}"
                if year_dir.exists():
                    # Find debt_equity JSON files
                    debt_equity_files = list(year_dir.glob("debt_equity_result_universal_*.json"))
                    
                    if debt_equity_files:
                        debt_equity_file = debt_equity_files[0]
                        
                        try:
                            with open(debt_equity_file, 'r', encoding='utf-8') as f:
                                debt_equity_data = json.load(f)
                            
                            # Extract ROE value
                            roe = debt_equity_data.get("equity_liability", {}).get("equity", {}).get("roe")
                            
                            if roe is not None and isinstance(roe, (int, float)):
                                roe_values.append(float(roe))
                                found_years.append(year)
                                logger.info(f"  📈 Year {year}: ROE = {roe:.2%}")
                            else:
                                logger.warning(f"  ⚠️ Year {year}: ROE not found or invalid in {debt_equity_file.name}")
                        
                        except Exception as e:
                            logger.warning(f"  ⚠️ Year {year}: Error reading {debt_equity_file.name}: {e}")
                    else:
                        logger.warning(f"  ⚠️ Year {year}: No debt_equity file found in {year_dir}")
                else:
                    logger.warning(f"  ⚠️ Year {year}: Directory {year_dir} not found")
            
            # Calculate geometric mean if we have enough data
            # if len(roe_values) >= 2:  # At least 2 years of data
            #     # Handle negative ROE values for geometric mean
            #     # Convert to positive for calculation, then adjust sign
            #     abs_roe_values = [abs(roe) + 0.001 for roe in roe_values]  # Add small epsilon to avoid zero
                
            #     # Calculate geometric mean
            #     product = 1.0
            #     for roe in abs_roe_values:
            #         product *= roe
                
            #     geometric_mean = product ** (1.0 / len(abs_roe_values))
                
            #     # Adjust sign based on majority of ROE values
            #     negative_count = sum(1 for roe in roe_values if roe < 0)
            #     if negative_count > len(roe_values) / 2:
            #         geometric_mean = -geometric_mean
                
            #     # Apply reasonable bounds
            #     geometric_mean = max(-1.00, min(geometric_mean, 2.00))
                
            #     logger.info(f"✅ Geometric Mean ROE ({len(roe_values)} years): {geometric_mean:.2%}")
            #     logger.info(f"📅 Years used: {found_years}")
                
            #     return geometric_mean
            
            # else:
            #     logger.warning(f"⚠️ Insufficient ROE data: found {len(roe_values)} years, need at least 2")
            #     return None
            
            if len(roe_values) < 2:
                logger.warning(f"⚠️ Insufficient ROE data: found {len(roe_values)} years, need at least 2")
                return None

            try:
                # ✅ Case 1: All ROEs > -100% (safe for (1+r) method)
                if all(roe > -1 for roe in roe_values):
                    product = 1.0
                    for roe in roe_values:
                        product *= (1 + roe)
                    geometric_mean = (product ** (1.0 / len(roe_values))) - 1

                else:
                    # ❌ Case 2: Values that would break the formula -> fallback
                    abs_roe_values = [abs(roe) + 0.001 for roe in roe_values]  # small epsilon
                    product = 1.0
                    for roe in abs_roe_values:
                        product *= roe
                    geometric_mean = product ** (1.0 / len(abs_roe_values))

                    # Adjust sign based on majority negatives
                    negative_count = sum(1 for roe in roe_values if roe < 0)
                    if negative_count > len(roe_values) / 2:
                        geometric_mean = -geometric_mean

                # Bound result between -100% and +200%
                geometric_mean = max(-1.00, min(geometric_mean, 2.00))

                logger.info(f"✅ Geometric Mean ROE ({len(roe_values)} years): {geometric_mean:.2%}")
                logger.info(f"📅 Years used: {found_years}")

                return geometric_mean

            except Exception as e:
                logger.error(f"❌ Error calculating geometric mean ROE: {e}")
                return None


        except Exception as e:
            logger.error(f"❌ Multi-year ROE calculation failed: {e}")
            return None

    def _calculate_wacc_metrics(self, values: Dict[str, Any], debt_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate WACC components"""
        logger.info("⚖️ Calculating WACC metrics...")
        
        # Cost of Debt = Finance Cost / Total Debt
        finance_cost = values["finance_cost"]
        total_debt = debt_metrics["total_debt"]
        
        # Handle debt-free companies correctly
        if total_debt > 0:
            cost_of_debt = finance_cost / total_debt
            logger.info(f"💰 Cost of Debt Calculation:")
            logger.info(f"   💸 Finance Cost: {finance_cost:,.0f}")
            logger.info(f"   🏦 Total Debt: {total_debt:,.0f}")
            logger.info(f"   📊 Raw Cost of Debt: {cost_of_debt:.4%}")
        else:
            # For debt-free companies, cost of debt should be 0
            cost_of_debt = 0.0
            logger.info(f"ℹ️ Debt-free company detected - setting cost of debt to 0%")
            if finance_cost > 0:
                logger.info(f"💡 Finance cost of {finance_cost:,.0f} with 0 debt might be interest income or other charges")
        
        # Convert to percentage and validate
        if cost_of_debt > 1.0:  # If > 100%, likely already in percentage form
            cost_of_debt = cost_of_debt / 100.0
        
        # Store the calculated cost of debt before bounds
        calculated_cost_of_debt = cost_of_debt

        # Reasonable bounds for cost of debt (0.1% to 20%) - BUT NOT for debt-free companies
        if total_debt > 0:
            cost_of_debt = max(0.001, min(cost_of_debt, 0.20))
        # For debt-free companies, keep cost_of_debt = 0.0 (don't apply minimum bounds)

        if abs(calculated_cost_of_debt - cost_of_debt) > 0.0001:
            logger.info(f"⚠️ Cost of debt adjusted from {calculated_cost_of_debt:.4%} to {cost_of_debt:.4%}")
        else:
            logger.info(f"✅ Cost of debt calculated: {cost_of_debt:.4%}")
        
        # ROE = Net Income / Total Equity
        net_income = values["net_income"]
        total_equity = values["total_equity"]
        
        roe = net_income / total_equity if total_equity > 0 else 0.12
        
        # Convert to percentage if needed and validate
        if abs(roe) > 1.0 and abs(roe) < 100:  # Likely in percentage form
            roe = roe / 100.0
        
        # Reasonable bounds for ROE (-100% to 200%) - more realistic for power companies
        roe = max(-1.00, min(roe, 2.00))
        
        wacc_metrics = {
            "cost_of_debt": cost_of_debt,
            "roe": roe,
            "wacc": 0.0  # Will be calculated below
        }
        
        logger.info(f"💰 Cost of Debt: {cost_of_debt:.2%}")
        logger.info(f"📈 ROE: {roe:.2%}")
        
        return wacc_metrics
    
    def _calculate_wacc_metrics_with_multi_year_roe(self, values: Dict[str, Any], debt_metrics: Dict[str, Any], output_dir: str = None) -> Dict[str, Any]:
        """
        Calculate WACC metrics using multi-year geometric mean ROE for financial assumptions
        Falls back to single-year ROE if multi-year data unavailable
        """
        logger.info("⚖️ Calculating WACC metrics with multi-year ROE...")
        
        # Cost of Debt = Finance Cost / Total Debt
        finance_cost = values["finance_cost"]
        total_debt = debt_metrics["total_debt"]
        
        # Handle debt-free companies correctly
        if total_debt > 0:
            cost_of_debt = finance_cost / total_debt
            logger.info(f"💰 Cost of Debt Calculation:")
            logger.info(f"   💸 Finance Cost: {finance_cost:,.0f}")
            logger.info(f"   🏦 Total Debt: {total_debt:,.0f}")
            logger.info(f"   📊 Raw Cost of Debt: {cost_of_debt:.4%}")
        else:
            # For debt-free companies, cost of debt should be 0
            cost_of_debt = 0.0
            logger.info(f"ℹ️ Debt-free company detected - setting cost of debt to 0%")
            if finance_cost > 0:
                logger.info(f"💡 Finance cost of {finance_cost:,.0f} with 0 debt might be interest income or other charges")
        
        # Convert to percentage and validate
        if cost_of_debt > 1.0:  # If > 100%, likely already in percentage form
            cost_of_debt = cost_of_debt / 100.0
        
        # Store the calculated cost of debt before bounds
        calculated_cost_of_debt = cost_of_debt

        # Reasonable bounds for cost of debt (0.1% to 20%) - BUT NOT for debt-free companies
        if total_debt > 0:
            cost_of_debt = max(0.001, min(cost_of_debt, 0.20))
        # For debt-free companies, keep cost_of_debt = 0.0 (don't apply minimum bounds)

        if abs(calculated_cost_of_debt - cost_of_debt) > 0.0001:
            logger.info(f"⚠️ Cost of debt adjusted from {calculated_cost_of_debt:.4%} to {cost_of_debt:.4%}")
        else:
            logger.info(f"✅ Cost of debt calculated: {cost_of_debt:.4%}")
        
        # Try to get multi-year geometric mean ROE
        roe = None
        if output_dir:
            # Try to get year from values first, then from balance sheet data
            latest_year = values.get("year")
            if latest_year is None:
                # Extract year from the calling context (passed via balance sheet)
                # This will be set in the calling method
                latest_year = getattr(self, '_current_year', None)
            
            if latest_year:
                roe = self.get_multi_year_geometric_mean_roe(output_dir, latest_year)
        
        # Fallback to single-year ROE calculation
        if roe is None:
            logger.info("📊 Falling back to single-year ROE calculation...")
            net_income = values["net_income"]
            total_equity = values["total_equity"]
            
            roe = net_income / total_equity if total_equity > 0 else 0.12
            
            # Convert to percentage if needed and validate
            if abs(roe) > 1.0 and abs(roe) < 100:  # Likely in percentage form
                roe = roe / 100.0
            
            # Reasonable bounds for ROE (-100% to 200%)
            roe = max(-1.00, min(roe, 2.00))
            
            logger.info(f"📈 Single-year ROE: {roe:.2%}")
        
        wacc_metrics = {
            "cost_of_debt": cost_of_debt,
            "roe": roe,
            "wacc": 0.0  # Will be calculated below
        }
        
        logger.info(f"💰 Cost of Debt: {cost_of_debt:.2%}")
        logger.info(f"📈 Final ROE (for WACC): {roe:.2%}")
        
        return wacc_metrics

    def _get_country_specific_rates(self, bs: Dict[str, Any]) -> Dict[str, Any]:
        """Get country-specific rates for depreciation, tax, and transition credit (Enhanced)"""
        logger.info("🌍 Getting country-specific rates...")
        
        country = bs.get("country", "Unknown")
        currency = bs.get("currency", "Unknown")
        
        # Get depreciation rate based on country (enhanced)
        depreciation_rate = self.DEPRECIATION_RATES.get(country, self.DEPRECIATION_RATES["Unknown"])
        
        # Get transition credit based on currency (enhanced)
        transition_credit = self.TRANSITION_CREDITS.get(currency, self.TRANSITION_CREDITS["Unknown"])
        
        # Get tax rate based on country (enhanced)
        tax_rate = self.TAX_RATES.get(country, self.TAX_RATES["Unknown"])
        
        country_rates = {
            "depreciation_rate": depreciation_rate,
            "tax_rate": tax_rate,
            "transition_credit": transition_credit
        }
        
        logger.info(f"🏗️ Depreciation Rate ({country}): {depreciation_rate:.1%}")
        logger.info(f"🏛️ Tax Rate ({country}): {tax_rate:.1%}")
        logger.info(f"💰 Transition Credit ({currency}): {transition_credit:,.1f}")
        
        return country_rates

    def _get_currency_information(self, bs: Dict[str, Any]) -> Dict[str, Any]:
        """Get currency information with exchange rates"""
        logger.info("💱 Getting currency information...")
        
        currency = bs.get("currency", "Unknown")
        
        if currency == "Unknown":
            return {
                "local_currency": "Unknown",
                "usd_exchange_rate": 1.0,
                "eur_exchange_rate": 1.0,
                "rate_timestamp": datetime.now().isoformat(),
                "rate_source": "unknown_currency"
            }
        
        # Get exchange rates
        try:
            usd_rate = self.get_exchange_rate(currency, "USD")
            eur_rate = self.get_exchange_rate(currency, "EUR")
            
            currency_info = {
                "local_currency": currency,
                "usd_exchange_rate": usd_rate,
                "eur_exchange_rate": eur_rate,
                "rate_timestamp": datetime.now().isoformat(),
                "rate_source": "api_with_fallback"
            }
            
            logger.info(f"💱 Currency: {currency}")
            logger.info(f"💵 1 {currency} = {usd_rate:.4f} USD")
            logger.info(f"💶 1 {currency} = {eur_rate:.4f} EUR")
            
            return currency_info
            
        except Exception as e:
            logger.warning(f"⚠️ Currency information failed: {e}")
            return {
                "local_currency": currency,
                "usd_exchange_rate": 1.0,
                "eur_exchange_rate": 1.0,
                "rate_timestamp": datetime.now().isoformat(),
                "rate_source": "error_fallback"
            }

    def _calculate_working_capital_interest(self, values: Dict[str, Any]) -> float:
        """Calculate working capital interest (legacy method)"""
        logger.info("💼 Calculating working capital interest (legacy)...")
        
        short_term_debt = values["short_term_debt"]
        finance_cost = values["finance_cost"]
        
        if short_term_debt > 0:
            # Estimate working capital portion (typically 20-30% of finance cost)
            estimated_wc_cost = finance_cost * 0.25  # 25% assumption
            working_capital_interest = estimated_wc_cost / short_term_debt
            
            # Reasonable bounds (5% to 25%)
            working_capital_interest = max(0.05, min(working_capital_interest, 0.25))
        else:
            working_capital_interest = 0.12  # 12% default
        
        logger.info(f"💼 Working Capital Interest (legacy): {working_capital_interest:.2%}")
        return working_capital_interest

    def _calculate_working_capital_interest_enhanced(self, values: Dict[str, Any], 
                                                   wacc_metrics: Dict[str, Any]) -> float:
        """Calculate working capital interest with enhanced logic: cost_of_debt + 0.02, capped at 0.11"""
        logger.info("💼 Calculating working capital interest (enhanced)...")
        
        # Get cost of debt from WACC metrics
        cost_of_debt = wacc_metrics.get("cost_of_debt", 0.08)
        
        # New formula: cost_of_debt + 0.02
        working_capital_interest = cost_of_debt + 0.02
        
        # Cap at 0.11 (11%)
        if working_capital_interest > 0.11:
            working_capital_interest = 0.11
            logger.info(f"💼 Working capital interest capped at 11%")
        
        logger.info(f"💼 Cost of Debt: {cost_of_debt:.2%}")
        logger.info(f"💼 Working Capital Interest (enhanced): {working_capital_interest:.2%}")
        logger.info(f"💼 Formula: cost_of_debt ({cost_of_debt:.2%}) + 2% = {working_capital_interest:.2%}")
        
        return working_capital_interest

    def _calculate_final_wacc(self, wacc_metrics: Dict[str, Any], debt_metrics: Dict[str, Any], 
                            country_rates: Dict[str, Any]) -> float:
        """Calculate final WACC using the standard formula"""
        logger.info("⚖️ Calculating final WACC...")
        
        # WACC = E/(E+D) × Re + D/(E+D) × Rd × (1-Tc)
        # Where: E = Equity, D = Debt, Re = ROE, Rd = Cost of Debt, Tc = Tax Rate
        
        equity_share = debt_metrics["equity_share"]
        debt_share = debt_metrics["debt_share"]
        roe = wacc_metrics["roe"]
        cost_of_debt = wacc_metrics["cost_of_debt"]
        tax_rate = country_rates["tax_rate"]
        
        # Calculate WACC
        equity_component = equity_share * roe
        debt_component = debt_share * cost_of_debt * (1 - tax_rate)
        wacc = equity_component + debt_component
        
        # Reasonable bounds for WACC - adjusted for debt-free companies
        if debt_share == 0:
            # For debt-free companies, WACC = ROE (can be higher than 25%)
            wacc = max(0.02, min(wacc, 2.00))  # Up to 200% for high-performing companies
            logger.info(f"ℹ️ Debt-free company: WACC = ROE = {wacc:.2%}")
        else:
            # For companies with debt, use standard bounds
            wacc = max(0.02, min(wacc, 0.25))
        
        logger.info(f"📊 Equity Component: {equity_share:.1%} × {roe:.2%} = {equity_component:.2%}")
        logger.info(f"🏦 Debt Component: {debt_share:.1%} × {cost_of_debt:.2%} × (1-{tax_rate:.1%}) = {debt_component:.2%}")
        logger.info(f"⚖️ Final WACC: {wacc:.2%}")
        
        return wacc

    def _log_calculation_summary(self, financial_metrics: Dict[str, Any]) -> None:
        """Log comprehensive calculation summary"""
        logger.info("📊 FINANCIAL METRICS CALCULATION SUMMARY:")
        
        # Company info
        logger.info(f"  🏭 Company: {financial_metrics.get('plant_name', 'Unknown')}")
        logger.info(f"  🌍 Country: {financial_metrics.get('extracted_country', 'Unknown')}")
        logger.info(f"  💱 Currency: {financial_metrics.get('extracted_currency', 'Unknown')}")
        
        # Enhanced metadata
        metadata = financial_metrics.get('calculation_metadata', {})
        logger.info(f"  🌍 Countries Supported: {metadata.get('countries_supported', 0)}")
        logger.info(f"  💱 Currencies Supported: {metadata.get('currencies_supported', 0)}")
        
        # Currency information
        currency_info = financial_metrics.get('currency_information', {})
        if currency_info.get('local_currency') != 'Unknown':
            logger.info(f"  💵 USD Rate: {currency_info.get('usd_exchange_rate', 0):.4f}")
            logger.info(f"  💶 EUR Rate: {currency_info.get('eur_exchange_rate', 0):.4f}")
        
        # Key financial values
        logger.info(f"  🏗️ Gross Block: {financial_metrics.get('gross_block', 0):,.0f}")
        logger.info(f"  📊 WDV IT: {financial_metrics.get('wdv_it', 0):,.0f}")
        logger.info(f"  🏛️ Tax Rate: {financial_metrics.get('corporate_tax_rate_static', 0):.1%}")
        logger.info(f"  🏗️ Depreciation Rate: {financial_metrics.get('depreciation_rate', 0):.1%}")
        logger.info(f"  💰 Transition Credit: {financial_metrics.get('transition_credit_base', 0):,.1f}")
        
        # WACC components
        wacc = financial_metrics.get('wacc_breakup', {})
        logger.info(f"  💰 Cost of Debt: {wacc.get('cost_of_debt', 0):.2%}")
        logger.info(f"  📈 ROE: {wacc.get('roe', 0):.2%}")
        logger.info(f"  ⚖️ WACC: {wacc.get('wacc', 0):.2%}")
        logger.info(f"  🏦 Debt Share: {wacc.get('debt', 0):.1%}")
        logger.info(f"  📈 Equity Share: {wacc.get('equity', 0):.1%}")
        logger.info(f"  💼 Working Capital Interest: {financial_metrics.get('working_capital_interest', 0):.2%}")

    def calculate_and_finalize_wacc(self, financial_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Final WACC calculation and update"""
        logger.info("🔧 Finalizing WACC calculation...")
        
        try:
            wacc_breakup = financial_metrics.get("wacc_breakup", {})
            logger.info(f"🔍 WACC breakup before calculation: {wacc_breakup}")
            
            # Extract components
            equity_share = wacc_breakup.get("equity", 0.5)
            debt_share = wacc_breakup.get("debt", 0.5)
            roe = wacc_breakup.get("roe", 0.12)
            cost_of_debt = wacc_breakup.get("cost_of_debt", 0.08)
            tax_rate = wacc_breakup.get("tax_rate", 0.25)
            
            logger.info(f"📊 WACC Components:")
            logger.info(f"   📈 Equity Share: {equity_share:.1%}")
            logger.info(f"   🏦 Debt Share: {debt_share:.1%}")
            logger.info(f"   📊 ROE: {roe:.2%}")
            logger.info(f"   💰 Cost of Debt: {cost_of_debt:.2%}")
            logger.info(f"   🏛️ Tax Rate: {tax_rate:.1%}")
            
            # Calculate final WACC
            final_wacc = self._calculate_final_wacc(
                {"roe": roe, "cost_of_debt": cost_of_debt},
                {"equity_share": equity_share, "debt_share": debt_share},
                {"tax_rate": tax_rate}
            )
            
            # Update WACC in the breakup
            if "wacc_breakup" not in financial_metrics:
                financial_metrics["wacc_breakup"] = {}
            financial_metrics["wacc_breakup"]["wacc"] = final_wacc
            
            logger.info(f"✅ WACC calculation finalized: {final_wacc:.2%}")
            logger.info(f"🔍 WACC breakup after calculation: {financial_metrics['wacc_breakup']}")
            return financial_metrics
            
        except Exception as e:
            logger.error(f"❌ WACC finalization failed: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            # Set default WACC if calculation fails
            if "wacc_breakup" not in financial_metrics:
                financial_metrics["wacc_breakup"] = {}
            financial_metrics["wacc_breakup"]["wacc"] = 0.10  # 10% default
            logger.info(f"🔧 Set default WACC: 10.00%")
            return financial_metrics


# Test function
def test_calculator():
    """Test the enhanced financial metrics calculator"""
    print("🧪 Testing Enhanced Financial Metrics Calculator...")
    
    calculator = FinancialMetricsCalculator()
    
    # Test exchange rate functionality
    print("\n💱 Testing Exchange Rate Functionality:")
    try:
        usd_to_eur = calculator.get_exchange_rate("USD", "EUR")
        print(f"USD to EUR: {usd_to_eur}")
        
        conversion = calculator.convert_currency(1000, "USD", "EUR")
        print(f"Convert 1000 USD: {conversion}")
        
    except Exception as e:
        print(f"Exchange rate test error: {e}")
    
    # Test with sample data
    sample_bs = {
        "balance_sheet": {
            "country": "United Kingdom",  # Test with new country
            "currency": "GBP",           # Test with new currency
            "plant_name": "Test UK Company Ltd",
            "Property, plant and equipment": 50000000000,
            "current_assets": 8000000000,
            "current_liabilities": 12000000000,
            "short_term_borrowings": 4500000000,
            "Long_Term_borrowing": 29000000000,
            "total_equity": 25000000000
        }
    }
    
    sample_pl = {
        "profit_loss": {
            "revenue": 15000000000,
            "finance_cost": 3500000000,
            "net_income": 2800000000
        }
    }
    
    try:
        result = calculator.calculate_financial_metrics(sample_bs, sample_pl)
        print("\n✅ Enhanced calculator test completed successfully!")
        print(f"🎯 WACC calculated: {result['wacc_breakup']['wacc']:.2%}")
        print(f"🌍 Country: {result['extracted_country']}")
        print(f"💱 Currency: {result['extracted_currency']}")
        print(f"🏛️ Tax Rate: {result['corporate_tax_rate_static']:.1%}")
        print(f"🏗️ Depreciation Rate: {result['depreciation_rate']:.1%}")
        
        # Test currency information
        if 'currency_information' in result:
            curr_info = result['currency_information']
            print(f"💵 USD Rate: {curr_info.get('usd_exchange_rate', 0):.4f}")
            print(f"💶 EUR Rate: {curr_info.get('eur_exchange_rate', 0):.4f}")
        
    except Exception as e:
        print(f"❌ Enhanced calculator test failed: {e}")


if __name__ == "__main__":
    test_calculator()